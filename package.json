{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^2.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.2.1", "vite": "^6.0.11", "vue": "^3.4.0"}, "dependencies": {"@codemirror/lang-markdown": "^6.3.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.7", "chart.js": "^4.4.9", "codemirror": "^6.0.1", "markdown-it": "^14.1.0", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markmap-lib": "^0.18.11", "markmap-view": "^0.18.10", "remixicon": "^4.6.0", "sal.js": "^0.8.5"}}