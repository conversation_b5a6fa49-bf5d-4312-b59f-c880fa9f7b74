<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('study_sessions', function (Blueprint $table) {
            $table->boolean('is_pomodoro')->default(false);
            $table->integer('study_duration_minutes')->nullable(); // Duração configurada para estudo
            $table->integer('break_duration_minutes')->nullable(); // Duração configurada para descanso
            $table->integer('completed_pomodoros')->default(0); // Número de pomodoros completados
            $table->boolean('is_break')->default(false); // Se está em intervalo
            $table->timestamp('paused_at')->nullable(); // Quando foi pausado
            $table->integer('paused_duration_seconds')->default(0); // Tempo total pausado
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('study_sessions', function (Blueprint $table) {
            $table->dropColumn([
                'is_pomodoro',
                'study_duration_minutes',
                'break_duration_minutes',
                'completed_pomodoros',
                'is_break',
                'paused_at',
                'paused_duration_seconds',
            ]);
        });
    }
};
