<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar usuário administrador se não existir
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrador',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        echo "Usuário administrador criado:\n";
        echo "Email: <EMAIL>\n";
        echo "Senha: admin123\n";
    }
}
