<?php

namespace Database\Seeders;

use App\Models\Discipline;
use App\Models\DisciplineGroup;
use App\Models\User;
use Illuminate\Database\Seeder;

class DisciplineGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users who have disciplines
        $users = User::whereHas('disciplines')->get();

        foreach ($users as $user) {
            // Create a default "General" group for each user
            $generalGroup = DisciplineGroup::create([
                'name' => 'Geral',
                'description' => 'Grupo padrão para todas as disciplinas',
                'is_default' => true,
                'user_id' => $user->id,
            ]);

            // Assign all existing disciplines to the "General" group
            Discipline::where('user_id', $user->id)
                ->whereNull('discipline_group_id')
                ->update(['discipline_group_id' => $generalGroup->id]);
        }
    }
}
