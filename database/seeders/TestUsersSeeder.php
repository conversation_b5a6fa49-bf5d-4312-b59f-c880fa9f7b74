<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class TestUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Usuário pendente
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Usuário Pendente',
                'password' => Hash::make('123456'),
                'role' => 'user',
                'status' => 'pending',
            ]
        );

        // Usuário bloqueado
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Usuário Bloqueado',
                'password' => Hash::make('123456'),
                'role' => 'user',
                'status' => 'blocked',
                'email_verified_at' => now(),
            ]
        );

        // Usuário ativo
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Usuário Ativo',
                'password' => Hash::make('123456'),
                'role' => 'user',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        echo "Usuários de teste criados:\n";
        echo "1. Pendente: <EMAIL> / 123456\n";
        echo "2. Bloqueado: <EMAIL> / 123456\n";
        echo "3. Ativo: <EMAIL> / 123456\n";
    }
}
