<script setup>
import { ref } from "vue";
import ApplicationLogo from "@/Components/ApplicationLogo.vue";
import Dropdown from "@/Components/Dropdown.vue";
import DropdownLink from "@/Components/DropdownLink.vue";
import NavLink from "@/Components/NavLink.vue";
import ResponsiveNavLink from "@/Components/ResponsiveNavLink.vue";
import Footer from "@/Components/Footer.vue";
import { Link } from "@inertiajs/vue3";

const showingNavigationDropdown = ref(false);
</script>

<template>
  <div class="flex flex-col min-h-screen">
    <div class="flex-grow">
      <nav class="bg-indigo-600 border-b border-indigo-500">
        <!-- Primary Navigation Menu -->
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div class="flex h-16 justify-between">
            <div class="flex">
              <!-- Logo -->
              <div class="flex shrink-0 items-center">
                <Link :href="route('dashboard')" class="flex items-center">
                  <img
                    src="/images/logo_branco.svg"
                    alt="Logo"
                    class="h-10 w-auto"
                  />
                  <span
                    class="ml-2 text-white font-semibold text-lg hidden sm:block"
                    >Estudus</span
                  >
                </Link>
              </div>

              <!-- Navigation Links -->
              <div class="hidden space-x-8 sm:ms-10 sm:flex">
                <NavLink
                  :href="route('dashboard')"
                  :active="route().current('dashboard')"
                >
                  Dashboard
                </NavLink>
                <NavLink
                  :href="route('discipline-groups.index')"
                  :active="route().current('discipline-groups.*')"
                >
                  Grupos
                </NavLink>
                <NavLink
                  :href="route('disciplines.index')"
                  :active="route().current('disciplines.*')"
                >
                  Disciplinas
                </NavLink>
                <NavLink
                  :href="route('lessons.index')"
                  :active="route().current('lessons.*')"
                >
                  Aulas
                </NavLink>
                <NavLink
                  :href="route('revisions.index')"
                  :active="route().current('revisions.*')"
                >
                  Revisões
                </NavLink>
                <NavLink
                  :href="route('study-sessions.index')"
                  :active="route().current('study-sessions.*')"
                >
                  Sessões
                </NavLink>
                <NavLink
                  :href="route('calendar.index')"
                  :active="route().current('calendar.*')"
                >
                  Calendário
                </NavLink>
              </div>
            </div>

            <div class="hidden sm:ms-6 sm:flex sm:items-center">
              <!-- Settings Dropdown -->
              <div class="relative ms-3">
                <Dropdown align="right" width="48">
                  <template #trigger>
                    <span class="inline-flex rounded-md">
                      <button
                        type="button"
                        class="inline-flex items-center rounded-md border border-transparent bg-indigo-700 px-3 py-2 text-sm font-medium leading-4 text-white transition duration-150 ease-in-out hover:bg-indigo-800 focus:outline-none"
                      >
                        {{ $page.props.auth.user.name }}

                        <svg
                          class="-me-0.5 ms-2 h-4 w-4"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </button>
                    </span>
                  </template>

                  <template #content>
                    <DropdownLink :href="route('profile.edit')">
                      Perfil
                    </DropdownLink>
                    <DropdownLink
                      v-if="$page.props.auth.user.role === 'admin'"
                      :href="route('admin.users.index')"
                    >
                      Gerenciar Usuários
                    </DropdownLink>
                    <DropdownLink
                      :href="route('logout')"
                      method="post"
                      as="button"
                    >
                      Sair
                    </DropdownLink>
                  </template>
                </Dropdown>
              </div>
            </div>

            <!-- Hamburger -->
            <div class="-me-2 flex items-center sm:hidden">
              <button
                @click="showingNavigationDropdown = !showingNavigationDropdown"
                class="inline-flex items-center justify-center rounded-md p-2 text-indigo-200 transition duration-150 ease-in-out hover:bg-indigo-700 hover:text-white focus:bg-indigo-700 focus:text-white focus:outline-none"
              >
                <svg
                  class="h-6 w-6"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    :class="{
                      hidden: showingNavigationDropdown,
                      'inline-flex': !showingNavigationDropdown,
                    }"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                  <path
                    :class="{
                      hidden: !showingNavigationDropdown,
                      'inline-flex': showingNavigationDropdown,
                    }"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Responsive Navigation Menu -->
        <div
          :class="{
            block: showingNavigationDropdown,
            hidden: !showingNavigationDropdown,
          }"
          class="sm:hidden"
        >
          <div class="space-y-1 pb-3 pt-2">
            <ResponsiveNavLink
              :href="route('dashboard')"
              :active="route().current('dashboard')"
            >
              Dashboard
            </ResponsiveNavLink>
            <ResponsiveNavLink
              :href="route('discipline-groups.index')"
              :active="route().current('discipline-groups.*')"
            >
              Grupos
            </ResponsiveNavLink>
            <ResponsiveNavLink
              :href="route('disciplines.index')"
              :active="route().current('disciplines.*')"
            >
              Disciplinas
            </ResponsiveNavLink>
            <ResponsiveNavLink
              :href="route('lessons.index')"
              :active="route().current('lessons.*')"
            >
              Aulas
            </ResponsiveNavLink>
            <ResponsiveNavLink
              :href="route('study-sessions.index')"
              :active="route().current('study-sessions.*')"
            >
              Sessões
            </ResponsiveNavLink>
            <ResponsiveNavLink
              :href="route('revisions.index')"
              :active="route().current('revisions.*')"
            >
              Revisões
            </ResponsiveNavLink>
            <ResponsiveNavLink
              :href="route('calendar.index')"
              :active="route().current('calendar.*')"
            >
              Calendário
            </ResponsiveNavLink>
          </div>

          <!-- Responsive Settings Options -->
          <div class="border-t border-indigo-700 pb-1 pt-4">
            <div class="px-4">
              <div class="text-base font-medium text-white">
                {{ $page.props.auth.user.name }}
              </div>
              <div class="text-sm font-medium text-indigo-200">
                {{ $page.props.auth.user.email }}
              </div>
            </div>

            <div class="mt-3 space-y-1">
              <ResponsiveNavLink :href="route('profile.edit')">
                Perfil
              </ResponsiveNavLink>
              <ResponsiveNavLink
                v-if="$page.props.auth.user.role === 'admin'"
                :href="route('admin.users.index')"
              >
                Gerenciar Usuários
              </ResponsiveNavLink>
              <ResponsiveNavLink
                :href="route('logout')"
                method="post"
                as="button"
              >
                Sair
              </ResponsiveNavLink>
            </div>
          </div>
        </div>
      </nav>

      <!-- Page Heading -->
      <header class="bg-white shadow" v-if="$slots.header">
        <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          <slot name="header" />
        </div>
      </header>

      <!-- Page Content -->
      <main class="bg-gray-50">
        <slot />
      </main>
    </div>

    <!-- Footer -->
    <Footer />
  </div>
</template>
