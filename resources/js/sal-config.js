import sal from 'sal.js';
import 'sal.js/dist/sal.css';

// Inicializa o Sal.js com configurações personalizadas
export function initSal() {
  sal({
    threshold: 0.1,
    once: false,
    animateClassName: 'sal-animate',
    disabledClassName: 'sal-disabled',
    enterEventName: 'sal:in',
    exitEventName: 'sal:out',
  });
}

// Reinicializa o Sal.js (útil após mudanças no DOM)
export function refreshSal() {
  sal();
}
