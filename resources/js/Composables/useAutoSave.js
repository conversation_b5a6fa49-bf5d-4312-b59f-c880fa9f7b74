import { ref, watch, onUnmounted } from 'vue';
import { router } from '@inertiajs/vue3';

export function useAutoSave(options = {}) {
  const {
    url,
    data = {},
    delay = 2000, // 2 segundos de delay
    enabled = true,
    onSuccess = () => {},
    onError = () => {},
  } = options;

  const isSaving = ref(false);
  const lastSaved = ref(null);
  const saveStatus = ref('idle'); // 'idle', 'saving', 'saved', 'error'
  const saveMessage = ref('');
  
  let saveTimeout = null;
  let lastSaveData = null;

  // Função para salvar os dados
  const save = async (saveData = data) => {
    if (!enabled || isSaving.value) return;

    // Verificar se os dados mudaram desde a última vez
    const currentDataString = JSON.stringify(saveData);
    if (currentDataString === lastSaveData) return;

    isSaving.value = true;
    saveStatus.value = 'saving';
    saveMessage.value = 'Salvando...';

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(saveData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        lastSaved.value = new Date();
        saveStatus.value = 'saved';
        saveMessage.value = result.message || 'Salvo automaticamente';
        lastSaveData = currentDataString;
        onSuccess(result);
      } else {
        throw new Error(result.message || 'Erro ao salvar');
      }
    } catch (error) {
      console.error('Auto save error:', error);
      saveStatus.value = 'error';
      saveMessage.value = 'Erro ao salvar automaticamente';
      onError(error);
    } finally {
      isSaving.value = false;
      
      // Limpar a mensagem após alguns segundos
      setTimeout(() => {
        if (saveStatus.value === 'saved' || saveStatus.value === 'error') {
          saveStatus.value = 'idle';
          saveMessage.value = '';
        }
      }, 3000);
    }
  };

  // Função para agendar um save
  const scheduleSave = (saveData = data) => {
    if (!enabled) return;

    // Cancelar save anterior se existir
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    // Agendar novo save
    saveTimeout = setTimeout(() => {
      save(saveData);
    }, delay);
  };

  // Função para forçar save imediato
  const forceSave = (saveData = data) => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }
    save(saveData);
  };

  // Função para cancelar save pendente
  const cancelSave = () => {
    if (saveTimeout) {
      clearTimeout(saveTimeout);
      saveTimeout = null;
    }
  };

  // Limpar timeout quando o componente for desmontado
  onUnmounted(() => {
    cancelSave();
  });

  return {
    isSaving,
    lastSaved,
    saveStatus,
    saveMessage,
    save,
    scheduleSave,
    forceSave,
    cancelSave,
  };
}

// Composable específico para mapas mentais
export function useMindMapAutoSave(lessonId, mindMapId = null) {
  const autoSave = useAutoSave({
    url: route('mind-maps.auto-save'),
    delay: 3000, // 3 segundos para mapas mentais
  });

  const saveContent = (content) => {
    const saveData = {
      content,
      lesson_id: lessonId,
    };

    if (mindMapId) {
      saveData.mind_map_id = mindMapId;
    }

    autoSave.scheduleSave(saveData);
  };

  const forceSaveContent = (content) => {
    const saveData = {
      content,
      lesson_id: lessonId,
    };

    if (mindMapId) {
      saveData.mind_map_id = mindMapId;
    }

    autoSave.forceSave(saveData);
  };

  return {
    ...autoSave,
    saveContent,
    forceSaveContent,
  };
}

// Composable específico para anotações
export function useNoteAutoSave(lessonId = null, noteId = null) {
  const autoSave = useAutoSave({
    url: route('notes.auto-save'),
    delay: 2000, // 2 segundos para anotações
  });

  const saveContent = (content) => {
    const saveData = {
      content,
    };

    if (noteId) {
      saveData.note_id = noteId;
    } else if (lessonId) {
      saveData.lesson_id = lessonId;
    }

    autoSave.scheduleSave(saveData);
  };

  const forceSaveContent = (content) => {
    const saveData = {
      content,
    };

    if (noteId) {
      saveData.note_id = noteId;
    } else if (lessonId) {
      saveData.lesson_id = lessonId;
    }

    autoSave.forceSave(saveData);
  };

  return {
    ...autoSave,
    saveContent,
    forceSaveContent,
  };
}
