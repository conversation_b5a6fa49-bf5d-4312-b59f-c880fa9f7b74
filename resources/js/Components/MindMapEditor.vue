<template>
  <div class="mindmap-editor-vertical">
    <!-- Editor Panel (Top) -->
    <div class="editor-panel">
      <div class="editor-header">
        <h3 class="editor-title">Editor de Markdown</h3>
        <div class="editor-actions">
          <button
            @click="resetToDefault"
            class="editor-button reset-button"
            title="Resetar para o modelo padrão"
          >
            <i class="ri-refresh-line"></i>
          </button>
          <button
            @click="copyToClipboard"
            class="editor-button copy-button"
            title="Copiar markdown"
          >
            <i class="ri-file-copy-line"></i>
          </button>
        </div>
      </div>

      <MarkdownEditor
        v-model="markdown"
        :dark-mode="false"
        :show-help="false"
      />
    </div>

    <!-- Preview Panel (Bottom) -->
    <div class="preview-panel">
      <div class="preview-header">
        <h3 class="preview-title">Visualização do Mapa Mental</h3>
        <div class="preview-actions">
          <button
            @click="toggleFullscreen"
            class="preview-button fullscreen-button"
            title="Tela cheia"
          >
            <i v-if="!isFullscreen" class="ri-fullscreen-line"></i>
            <i v-else class="ri-fullscreen-exit-line"></i>
            <span class="ml-1">Tela Cheia</span>
          </button>
          <button
            @click="saveMap"
            class="preview-button save-button"
            title="Salvar mapa mental"
          >
            <i class="ri-save-line"></i>
            <span class="ml-1">Salvar</span>
          </button>
        </div>
      </div>

      <div
        ref="svgContainer"
        class="editor-preview"
        :class="{ fullscreen: isFullscreen }"
      />
    </div>
  </div>
  <div class="editor-help mt-4">
    <div class="help-header">
      <h3 class="help-title">Guia de Sintaxe Markdown</h3>
      <p class="help-subtitle">
        Crie mapas mentais poderosos usando a sintaxe abaixo:
      </p>
    </div>

    <div class="help-content">
      <div class="help-section">
        <h4 class="section-title">Estrutura do Mapa</h4>
        <ul class="help-list">
          <li>
            <code># Título</code>
            <span class="help-desc">Nó principal (raiz do mapa)</span>
          </li>
          <li>
            <code>## Subtítulo</code>
            <span class="help-desc">Nó secundário (primeiro nível)</span>
          </li>
          <li>
            <code>### Subtítulo</code>
            <span class="help-desc">Nó terciário (segundo nível)</span>
          </li>
        </ul>
      </div>

      <div class="help-section">
        <h4 class="section-title">Listas e Itens</h4>
        <ul class="help-list">
          <li>
            <code>- Item</code>
            <span class="help-desc">Item de lista não ordenada</span>
          </li>
          <li>
            <code> - Subitem</code>
            <span class="help-desc">Subitem (use 2 espaços para indentar)</span>
          </li>
          <li>
            <code>1. Item</code>
            <span class="help-desc">Item de lista ordenada</span>
          </li>
          <li>
            <code>- [ ] Tarefa</code>
            <span class="help-desc">Item de lista não marcado</span>
          </li>
          <li>
            <code>- [x] Tarefa</code>
            <span class="help-desc">Item de lista marcado</span>
          </li>
        </ul>
      </div>

      <div class="help-section">
        <h4 class="section-title">Formatação de Texto</h4>
        <ul class="help-list">
          <li>
            <code>**texto**</code>
            <span class="help-desc">Texto em negrito</span>
          </li>
          <li>
            <code>*texto*</code> <span class="help-desc">Texto em itálico</span>
          </li>
          <li>
            <code>~~texto~~</code> <span class="help-desc">Texto riscado</span>
          </li>
          <li>
            <code>==texto==</code>
            <span class="help-desc">Texto destacado</span>
          </li>
          <li>
            <code>`código`</code> <span class="help-desc">Código inline</span>
          </li>
        </ul>
      </div>

      <div class="help-section">
        <h4 class="section-title">Elementos Avançados</h4>
        <ul class="help-list">
          <li>
            <div class="code-block">
              ```js<br />
              console.log('hello'); <br />```
            </div>
            <span class="help-desc"
              >Bloco de código com destaque de sintaxe</span
            >
          </li>
          <li>
            <div class="code-block">
              | Produto | Preço | <br />
              |-|-|<br />
              | Maçã | R$ 4 | <br />
              | Banana |R$ 2 |
            </div>
            <span class="help-desc">Tabela</span>
          </li>
          <li>
            <code>![texto alt](https://exemplo.com/imagem.jpg)</code>
            <span class="help-desc">Imagem</span>
          </li>
          <li>
            <code>[texto do link](https://exemplo.com)</code>
            <span class="help-desc">Link</span>
          </li>
        </ul>
      </div>

      <div class="help-section">
        <h4 class="section-title">Dicas</h4>
        <ul class="help-list tips-list">
          <li>
            <span class="tip-icon">💡</span> Use <strong>Tab</strong> para
            indentar e criar hierarquias
          </li>
          <li>
            <span class="tip-icon">💡</span> Comece sempre com um título
            principal usando <code>#</code>
          </li>
          <li>
            <span class="tip-icon">💡</span> Upload de imagens
            <a
              href="https://postimages.org/"
              target="_blank"
              class="ml-2 external-link"
              >postimages.org
            </a>
          </li>
          <li>
            <span class="tip-icon">💡</span> Use o botão
            <strong>Tela cheia</strong> para melhor visualização do mapa
          </li>
        </ul>
      </div>

      <div class="help-example">
        <h4 class="example-title">Exemplo</h4>
        <div class="example-content">
          <pre><code># Meu Mapa Mental
## Conceitos Principais
- Ideia 1
  - Subideia 1.1
  - Subideia 1.2
## Aplicações
- Exemplo 1
- Exemplo 2
  - Detalhes
    - **Importante**
    - *Observação*</code></pre>
        </div>
      </div>
    </div>

    <div>
      <br />
      <h4 class="section-title">Painel de Emojis</h4>
      <EmojiPanel />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick } from "vue";
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";
import MarkdownEditor from "@/Components/MarkdownEditor.vue";
import EmojiPanel from "@/Components/EmojiPanel.vue";

const props = defineProps({
  initialData: {
    type: [Object, String],
    default: () => ({
      content: `# Título\n\n## Subtópico\n- Item 1\n- Item 2`,
    }),
  },
});

const emit = defineEmits(["save"]);

// Estado do componente
const markdown = ref("");
const svgContainer = ref(null);
const isFullscreen = ref(false);
const transformer = new Transformer();
let markmap = null;

// Modelo padrão para resetar
const defaultMarkdown = `# Título\n\n## Subtópico\n- Item 1\n- Item 2\n\n## Outro Subtópico\n- Exemplo 1\n- Exemplo 2\n  - Subitem`;

// Inicializar o markdown com os dados iniciais
const initializeMarkdown = () => {
  if (typeof props.initialData === "string") {
    markdown.value = props.initialData;
  } else if (props.initialData && props.initialData.content) {
    markdown.value = props.initialData.content;
  } else {
    markdown.value = defaultMarkdown;
  }
};

// Função para renderizar o mapa mental
// Função para renderizar o mapa mental
const renderMindMap = async () => {
  await nextTick();
  if (!svgContainer.value) return;

  // Garantir que o conteúdo tenha pelo menos um cabeçalho válido
  if (!markdown.value.includes("#")) {
    markdown.value = `# ${markdown.value}`;
  }

  // Limpar o contêiner
  svgContainer.value.innerHTML = "";

  try {
    // Transformar o markdown em dados para o mapa mental
    const { root } = transformer.transform(markdown.value);

    // Verificar se o root é válido
    if (!root || !root.children || root.children.length === 0) {
      throw new Error("Dados do mapa mental inválidos");
    }

    // Criar o SVG
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");

    // Definir dimensões explícitas em pixels para evitar problemas de cálculo
    const containerWidth = svgContainer.value.clientWidth || 800;
    const containerHeight = isFullscreen.value ? window.innerHeight - 150 : 600;

    svg.setAttribute("width", containerWidth);
    svg.setAttribute("height", containerHeight);

    // Definir um viewBox explícito para ajudar na escala
    svg.setAttribute("viewBox", `0 0 ${containerWidth} ${containerHeight}`);

    // Adicionar o SVG ao DOM
    svgContainer.value.appendChild(svg);

    // Esperar o próximo ciclo de renderização
    await nextTick();

    // Usar a mesma abordagem que funciona no MindMapPreview.vue
    // Configuração mais simples sem opções complexas
    markmap = Markmap.create(
      svg,
      {
        // Opções básicas que não dependem de cálculos complexos
        paddingX: 20,
        paddingY: 10,
        duration: 400,
        autoFit: false, // Desativar autoFit para evitar cálculos que podem resultar em NaN
      },
      root
    );

    // Usar setTimeout mais longo para garantir que o SVG esteja completamente renderizado
    setTimeout(() => {
      try {
        // Tentar centralizar manualmente em vez de usar autoFit
        const { minX, maxX, minY, maxY } = markmap.state;
        if (
          isFinite(minX) &&
          isFinite(maxX) &&
          isFinite(minY) &&
          isFinite(maxY)
        ) {
          const width = maxX - minX;
          const height = maxY - minY;
          if (width > 0 && height > 0) {
            const scale = Math.min(
              containerWidth / (width + 100),
              containerHeight / (height + 100)
            );
            markmap.setData({
              ...markmap.state,
              scale: scale,
              x: containerWidth / 2,
              y: containerHeight / 2,
            });
          }
        }
      } catch (err) {
        console.error("Erro ao ajustar visualização:", err);
      }
    }, 200);
  } catch (error) {
    console.error("Erro ao renderizar o mapa mental:", error);
    svgContainer.value.innerHTML = `
      <div class="p-4 text-center text-red-500">
        Erro ao renderizar o mapa mental: ${error.message || "Formato inválido"}
      </div>
    `;
  }
};

// Função para alternar o modo de tela cheia
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;

  // Renderizar novamente após a mudança de estado
  setTimeout(() => {
    renderMindMap();
  }, 100);
};

// A função handleTab foi removida pois agora é tratada pelo componente MarkdownEditor

// Função para resetar para o modelo padrão
const resetToDefault = () => {
  if (
    confirm(
      "Tem certeza que deseja resetar o mapa mental para o modelo padrão? Todas as alterações serão perdidas."
    )
  ) {
    markdown.value = defaultMarkdown;
  }
};

// Função para copiar o markdown para a área de transferência
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(markdown.value);
    alert("Markdown copiado para a área de transferência!");
  } catch (err) {
    console.error("Erro ao copiar para a área de transferência:", err);
    alert(
      "Não foi possível copiar para a área de transferência. Tente novamente."
    );
  }
};

// Função para salvar o mapa mental
const saveMap = () => {
  emit("save", { content: markdown.value });
};

// Função para lidar com a tecla ESC no modo de tela cheia
const handleKeyDown = (event) => {
  if (event.key === "Escape" && isFullscreen.value) {
    isFullscreen.value = false;
    setTimeout(() => {
      renderMindMap();
    }, 100);
  }
};

// Inicializar o componente
onMounted(() => {
  initializeMarkdown();
  renderMindMap();

  // Adicionar evento de escuta para a tecla ESC
  window.addEventListener("keydown", handleKeyDown);

  // Adicionar evento de redimensionamento da janela
  window.addEventListener("resize", () => {
    if (isFullscreen.value) {
      renderMindMap();
    }
  });
});

// Limpar eventos quando o componente for desmontado
onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyDown);
  window.removeEventListener("resize", renderMindMap);
});

// Observar mudanças no markdown para renderizar o mapa mental
watch(markdown, () => {
  renderMindMap();
});
</script>

<style scoped>
.mindmap-editor-vertical {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
  position: relative;
}

/* Painel do editor */
.editor-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.editor-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.editor-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem;
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.editor-button:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.reset-button:hover {
  background-color: #fee2e2;
  color: #b91c1c;
}

.copy-button:hover {
  background-color: #e0f2fe;
  color: #0369a1;
}

.editor-help {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.help-header {
  margin-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.75rem;
}

.help-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #4338ca;
  margin-bottom: 0.25rem;
}

.help-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.help-section {
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.5rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px dashed #e5e7eb;
}

.help-list {
  padding-left: 0.5rem;
  color: #6b7280;
  list-style-type: none;
}

.help-list li {
  margin-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
}

.help-list code {
  background-color: #e5e7eb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: monospace;
  font-size: 0.875rem;
  color: #4338ca;
  display: inline-block;
  margin-bottom: 0.25rem;
  border: 1px solid #d1d5db;
}

.help-desc {
  font-size: 0.75rem;
  color: #6b7280;
  margin-left: 0.25rem;
}

.code-block {
  background-color: #e5e7eb;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-family: monospace;
  font-size: 0.75rem;
  color: #4338ca;
  margin-bottom: 0.25rem;
  white-space: pre-wrap;
  border: 1px solid #d1d5db;
}

.tips-list li {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.tip-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.external-link {
  color: #4338ca;
  text-decoration: none;
  font-weight: 500;
}

.external-link:hover {
  text-decoration: underline;
}

.help-example {
  grid-column: 1 / -1;
  margin-top: 1rem;
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.example-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.example-content {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}

.example-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 0.875rem;
  color: #1f2937;
  line-height: 1.5;
}

.editor-textarea {
  flex-grow: 1;
  height: 100%;
  min-height: 500px;
  padding: 1rem;
  font-family: monospace;
  font-size: 1rem;
  line-height: 1.5;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  resize: none;
  outline: none;
  transition: border-color 0.2s;
}

.editor-textarea:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

/* Painel de visualização */
.preview-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 2rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.preview-title {
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

.preview-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.preview-button svg {
  margin-right: 0.25rem;
}

.preview-button:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.fullscreen-button:hover {
  background-color: #e0f2fe;
  color: #0369a1;
}

.save-button {
  background-color: #6366f1;
  color: white;
}

.save-button:hover {
  background-color: #4f46e5;
  color: white;
}

.editor-preview {
  flex-grow: 1;
  height: 500px;
  overflow: auto;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.3s ease;
}

.editor-preview.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 50;
  background-color: white;
  border-radius: 0;
  padding: 2rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .preview-panel {
    margin-top: 1rem;
  }

  .editor-textarea,
  .editor-preview {
    height: 400px;
  }
}
</style>
