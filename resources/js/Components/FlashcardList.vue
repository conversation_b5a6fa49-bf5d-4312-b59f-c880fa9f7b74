<template>
  <div class="flashcards-section">
    <div class="section-header">
      <h3 class="text-lg font-semibold text-gray-800 mb-2">
        <i class="ri-file-list-3-line mr-2 text-indigo-600"></i>
        Flashcards
      </h3>
      <p v-if="flashcards.length === 0" class="text-gray-600 text-sm">
        Nenhum flashcard cadastrado para esta lição.
      </p>
      <p v-else class="text-gray-600 text-sm">
        {{ flashcards.length }}
        {{ flashcards.length === 1 ? "flashcard" : "flashcards" }} disponíveis
        para revisão.
      </p>
    </div>

    <div v-if="flashcards.length > 0" class="flashcards-container mt-4">
      <div class="flashcards-study-mode mb-6" v-if="!canEdit">
        <div class="flex justify-center">
          <button
            @click="startStudyMode"
            v-if="!studyMode"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            <i class="ri-play-line mr-1"></i> Iniciar Rev<PERSON> de Flashcards
          </button>
        </div>

        <div v-if="studyMode" class="study-container mt-4">
          <div class="flex justify-between items-center mb-4">
            <span class="text-sm text-gray-600"
              >{{ currentCardIndex + 1 }} de {{ flashcards.length }}</span
            >
            <button
              @click="exitStudyMode"
              class="text-gray-500 hover:text-gray-700"
            >
              <i class="ri-close-line"></i> Sair
            </button>
          </div>

          <div
            class="flashcard-study-card"
            :class="{ flipped: showBack }"
            @click="flipCard"
          >
            <div class="flashcard-inner">
              <div class="flashcard-front">
                <div class="p-6">
                  <h4 class="text-xl font-medium text-gray-800 mb-2">
                    {{ currentCard.front }}
                  </h4>
                  <p class="text-sm text-gray-500 mt-4">
                    Clique para ver a resposta
                  </p>
                </div>
              </div>
              <div class="flashcard-back">
                <div class="p-6">
                  <h4 class="text-lg font-medium text-gray-800 mb-4">
                    Resposta:
                  </h4>
                  <div class="text-gray-700">
                    <MarkdownRenderer :content="currentCard.back" />
                  </div>
                  <p class="text-sm text-gray-500 mt-4">
                    Clique para voltar à pergunta
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="navigation-buttons mt-6 flex justify-between">
            <button
              @click="prevCard"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
              :disabled="currentCardIndex === 0"
              :class="{
                'opacity-50 cursor-not-allowed': currentCardIndex === 0,
              }"
            >
              <i class="ri-arrow-left-line mr-1"></i> Anterior
            </button>
            <button
              @click="nextCard"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              :disabled="currentCardIndex === flashcards.length - 1"
              :class="{
                'opacity-50 cursor-not-allowed':
                  currentCardIndex === flashcards.length - 1,
              }"
            >
              Próximo <i class="ri-arrow-right-line ml-1"></i>
            </button>
          </div>
        </div>
      </div>

      <div
        v-if="!studyMode"
        class="flashcards-grid grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        <div
          v-for="flashcard in flashcards"
          :key="flashcard.id"
          class="flashcard-card bg-white rounded-lg shadow-sm p-4 border border-gray-200"
        >
          <div class="flashcard-header flex justify-between items-center mb-3">
            <h4 class="font-medium text-indigo-700">Flashcard</h4>
            <div class="flex space-x-2" v-if="canEdit">
              <button
                @click="editFlashcard(flashcard)"
                class="flex items-center rounded-lg bg-gray-100 px-3 py-1.5 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
              >
                <i class="ri-edit-line mr-1"></i>
                Editar
              </button>

              <button
                @click="deleteFlashcard(flashcard)"
                title="Excluir flashcard"
                class="flex items-center rounded-lg bg-red-50 px-3 py-1.5 text-xs font-medium text-red-700 transition-colors hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50"
              >
                <i class="ri-delete-bin-line mr-1"></i>
                Excluir
              </button>
            </div>
          </div>

          <div class="flashcard-content">
            <div class="mb-3">
              <h5 class="text-sm font-medium text-gray-500 mb-1">Frente:</h5>
              <p class="text-gray-800">{{ flashcard.front }}</p>
            </div>
            <div>
              <h5 class="text-sm font-medium text-gray-500 mb-1">Verso:</h5>
              <div class="text-gray-800">
                <MarkdownRenderer :content="flashcard.back" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="canEdit" class="add-flashcard-section mt-6">
      <button
        @click="showAddFlashcardForm = !showAddFlashcardForm"
        class="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors"
      >
        <i class="ri-add-circle-line mr-1"></i>
        {{ showAddFlashcardForm ? "Cancelar" : "Adicionar novo flashcard" }}
      </button>

      <div
        v-if="showAddFlashcardForm"
        class="flashcard-form mt-4 p-4 bg-gray-50 rounded-lg"
      >
        <h4 class="font-medium text-gray-800 mb-4">
          {{ editingFlashcard ? "Editar flashcard" : "Novo flashcard" }}
        </h4>

        <div class="form-group mb-4">
          <label
            for="flashcardFront"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Frente (Conceito/Pergunta)</label
          >
          <input
            id="flashcardFront"
            v-model="form.front"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            placeholder="Digite o conceito ou pergunta"
          />
        </div>

        <div class="form-group mb-4">
          <label
            for="flashcardBack"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Verso (Explicação/Resposta) - Suporta Markdown</label
          >
          <div class="mb-2">
            <MarkdownEditor
              v-model="form.back"
              :dark-mode="false"
              :show-help="true"
            />
          </div>
          <div class="text-xs text-gray-500 mt-1">
            Use Markdown para formatar o texto: **negrito**, *itálico*, listas,
            etc.
          </div>
        </div>

        <div class="form-actions flex justify-end space-x-3">
          <button
            @click="showAddFlashcardForm = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-colors"
          >
            Cancelar
          </button>
          <button
            @click="saveFlashcard"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
            :disabled="isSubmitting"
          >
            {{
              isSubmitting
                ? "Salvando..."
                : editingFlashcard
                ? "Atualizar"
                : "Salvar"
            }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { router } from "@inertiajs/vue3";
import axios from "axios";
import MarkdownEditor from "@/Components/MarkdownEditor.vue";
import MarkdownRenderer from "@/Components/MarkdownRenderer.vue";

const props = defineProps({
  flashcards: {
    type: Array,
    default: () => [],
  },
  lessonId: {
    type: Number,
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: false,
  },
});

const showAddFlashcardForm = ref(false);
const editingFlashcard = ref(null);
const isSubmitting = ref(false);
const studyMode = ref(false);
const currentCardIndex = ref(0);
const showBack = ref(false);

const form = reactive({
  front: "",
  back: "",
});

const currentCard = computed(() => {
  if (props.flashcards.length === 0) return { front: "", back: "" };
  return props.flashcards[currentCardIndex.value];
});

const resetForm = () => {
  form.front = "";
  form.back = "";
  editingFlashcard.value = null;
};

const editFlashcard = (flashcard) => {
  editingFlashcard.value = flashcard;
  form.front = flashcard.front;
  form.back = flashcard.back;
  showAddFlashcardForm.value = true;
};

const deleteFlashcard = async (flashcard) => {
  if (confirm("Tem certeza que deseja excluir este flashcard?")) {
    try {
      const response = await axios.delete(
        route("flashcards.destroy", flashcard.id)
      );

      if (response.data && response.data.success) {
        // Remover o flashcard localmente em vez de recarregar a página inteira
        const index = props.flashcards.findIndex((f) => f.id === flashcard.id);
        if (index !== -1) {
          props.flashcards.splice(index, 1);
        }

        // Exibir mensagem de sucesso
        alert(response.data.message || "Flashcard excluído com sucesso!");
      } else {
        alert("Erro ao excluir flashcard. Por favor, tente novamente.");
      }
    } catch (error) {
      console.error("Erro ao excluir flashcard:", error);
      alert("Erro ao excluir flashcard. Por favor, tente novamente.");
    }
  }
};

const saveFlashcard = async () => {
  // Validate form
  if (!form.front.trim() || !form.back.trim()) {
    alert("Por favor, preencha todos os campos.");
    return;
  }

  isSubmitting.value = true;

  try {
    if (editingFlashcard.value) {
      const response = await axios.put(
        route("flashcards.update", editingFlashcard.value.id),
        form
      );

      // Atualizar o flashcard localmente
      const index = props.flashcards.findIndex(
        (f) => f.id === editingFlashcard.value.id
      );
      if (index !== -1) {
        // Criar uma cópia atualizada do flashcard
        const updatedFlashcard = { ...props.flashcards[index] };
        updatedFlashcard.front = form.front;
        updatedFlashcard.back = form.back;

        // Atualizar o flashcard no array
        props.flashcards.splice(index, 1, updatedFlashcard);
      }

      //alert("Flashcard atualizado com sucesso!");
    } else {
      // Create new flashcard
      const response = await axios.post(
        route("flashcards.store", props.lessonId),
        form
      );

      if (response.data && response.data.flashcard) {
        // Adicionar o novo flashcard ao array local
        props.flashcards.push(response.data.flashcard);
        //alert("Flashcard adicionado com sucesso!");
      } else {
        // Recarregar a página para obter o novo flashcard
        router.reload();
      }
    }

    resetForm();
    showAddFlashcardForm.value = false;
  } catch (error) {
    console.error("Erro ao salvar flashcard:", error);

    // Exibir mensagem de erro mais detalhada
    if (error.response && error.response.data && error.response.data.errors) {
      const errorMessages = Object.values(error.response.data.errors).flat();
      alert(`Erro ao salvar flashcard: ${errorMessages.join("\n")}`);
    } else {
      alert(
        "Ocorreu um erro ao salvar o flashcard. Por favor, tente novamente."
      );
    }
  } finally {
    isSubmitting.value = false;
  }
};

const startStudyMode = () => {
  studyMode.value = true;
  currentCardIndex.value = 0;
  showBack.value = false;
};

const exitStudyMode = () => {
  studyMode.value = false;
};

const flipCard = () => {
  showBack.value = !showBack.value;
};

const nextCard = () => {
  if (currentCardIndex.value < props.flashcards.length - 1) {
    currentCardIndex.value++;
    showBack.value = false;
  }
};

const prevCard = () => {
  if (currentCardIndex.value > 0) {
    currentCardIndex.value--;
    showBack.value = false;
  }
};
</script>

<style scoped>
.flashcard-study-card {
  perspective: 1000px;
  height: 300px;
  cursor: pointer;
}

.flashcard-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flashcard-study-card.flipped .flashcard-inner {
  transform: rotateY(180deg);
}

.flashcard-front,
.flashcard-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #e5e7eb;
}

.flashcard-back {
  transform: rotateY(180deg);
  background-color: #f9fafb;
}
</style>
