<template>
  <div
    class="flex items-center justify-between cursor-pointer"
    @click="toggleExpanded"
  >
    <div class="flex items-center">
      <slot name="icon"></slot>
      <h3
        class="text-lg font-medium leading-6 text-indigo-700 dark:text-indigo-300"
      >
        {{ title }}
      </h3>
    </div>
    <div class="flex items-center">
      <slot name="actions"></slot>
      <button
        class="ml-2 p-1 text-gray-500 hover:text-gray-700 focus:outline-none"
        @click.stop="toggleExpanded"
      >
        <i
          class="ri-arrow-down-s-line text-xl transition-transform duration-200"
          :class="{ 'rotate-180': modelValue }"
        ></i>
      </button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(["update:modelValue"]);

const toggleExpanded = () => {
  emit("update:modelValue", !props.modelValue);
};
</script>
