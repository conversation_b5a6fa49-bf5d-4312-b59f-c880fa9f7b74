<template>
  <div class="flex items-center justify-between cursor-pointer" @click="toggleExpanded">
    <div class="flex items-center">
      <slot name="icon"></slot>
      <h3 class="text-lg font-medium leading-6 text-indigo-700 dark:text-indigo-300">
        {{ title }}
      </h3>
    </div>
    <div class="flex items-center">
      <slot name="actions"></slot>
      <button 
        class="ml-2 p-1 text-gray-500 hover:text-gray-700 focus:outline-none"
        @click.stop="toggleExpanded"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke-width="1.5"
          stroke="currentColor"
          class="h-5 w-5 transition-transform duration-200"
          :class="{ 'rotate-180': modelValue }"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            d="M19.5 8.25l-7.5 7.5-7.5-7.5"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  modelValue: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const toggleExpanded = () => {
  emit('update:modelValue', !props.modelValue);
};
</script>
