<template>
  <div class="study-session-timer">
    <div v-if="!activeSession" class="start-session">
      <!-- Discipline Group Selection -->
      <div class="mb-4">
        <label
          for="disciplineGroup"
          class="block text-sm font-medium text-gray-700"
        >
          Selecione um grupo de disciplinas
        </label>
        <select
          id="disciplineGroup"
          v-model="selectedDisciplineGroup"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
          <option value="" disabled>Selecione um grupo</option>
          <option
            v-for="group in disciplineGroups"
            :key="group.id"
            :value="group.id"
          >
            {{ group.name }}
          </option>
        </select>
      </div>

      <!-- Discipline Selection (only shown when a group is selected) -->
      <div class="mb-4" v-if="selectedDisciplineGroup">
        <label for="discipline" class="block text-sm font-medium text-gray-700">
          Selecione uma disciplina
        </label>
        <select
          id="discipline"
          v-model="selectedDiscipline"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
        >
          <option value="" disabled>Selecione uma disciplina</option>
          <option
            v-for="discipline in filteredDisciplines"
            :key="discipline.id"
            :value="discipline.id"
          >
            {{ discipline.name }}
          </option>
        </select>
      </div>

      <button
        @click="startSession"
        :disabled="!selectedDiscipline"
        class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <i class="ri-play-circle-line mr-2"></i>
        Iniciar Sessão de Estudo
      </button>
    </div>

    <div v-else class="active-session">
      <!-- Study Session Info -->
      <div
        class="session-info mb-4 p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800"
      >
        <h3
          class="text-lg font-semibold text-indigo-900 dark:text-indigo-100 mb-2"
        >
          <i class="ri-book-open-line mr-2"></i>
          Sessão de Estudo Ativa
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
          <div class="flex items-center">
            <i
              class="ri-folder-line mr-2 text-indigo-600 dark:text-indigo-400"
            ></i>
            <span class="text-gray-700 dark:text-gray-300">
              <strong>Grupo:</strong> {{ currentGroupName }}
            </span>
          </div>
          <div class="flex items-center">
            <i
              class="ri-book-line mr-2 text-indigo-600 dark:text-indigo-400"
            ></i>
            <span class="text-gray-700 dark:text-gray-300">
              <strong>Disciplina:</strong> {{ currentDisciplineName }}
            </span>
          </div>
        </div>
      </div>

      <div class="timer-display mb-4 text-center">
        <div class="text-4xl font-bold text-indigo-600">
          {{ formatTime(elapsedTime) }}
        </div>
        <div class="text-sm text-gray-500">
          Iniciado às {{ formatStartTime }}
        </div>
      </div>

      <div class="flex flex-col space-y-4">
        <div>
          <label
            for="notes"
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Anotações (opcional)
          </label>
          <textarea
            id="notes"
            v-model="notes"
            rows="3"
            class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="Adicione anotações sobre o que você estudou nesta sessão..."
          ></textarea>
        </div>

        <button
          @click="endSession"
          class="inline-flex items-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
        >
          <i class="ri-stop-circle-line mr-2"></i>
          Finalizar Sessão
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, watch } from "vue";
import axios from "axios";

const props = defineProps({
  disciplines: {
    type: Array,
    required: true,
  },
  disciplineGroups: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["sessionEnded"]);

const selectedDisciplineGroup = ref("");
const selectedDiscipline = ref("");
const activeSession = ref(null);
const startTime = ref(null);
const elapsedTime = ref(0);
const notes = ref("");
const startTimestamp = ref(null);
let timer = null;

// Computed property to filter disciplines based on selected group
const filteredDisciplines = computed(() => {
  if (!selectedDisciplineGroup.value) return [];

  // Find the selected group
  const group = props.disciplineGroups.find(
    (group) => group.id === selectedDisciplineGroup.value
  );

  // Return disciplines from that group
  return group ? group.disciplines : [];
});

// Reset discipline selection when group changes
watch(selectedDisciplineGroup, () => {
  selectedDiscipline.value = "";
});

// Get current group name
const currentGroupName = computed(() => {
  if (!selectedDisciplineGroup.value) return "";
  const group = props.disciplineGroups.find(
    (group) => group.id === selectedDisciplineGroup.value
  );
  return group ? group.name : "";
});

// Get current discipline name
const currentDisciplineName = computed(() => {
  if (!selectedDiscipline.value) return "";
  const discipline = filteredDisciplines.value.find(
    (discipline) => discipline.id === selectedDiscipline.value
  );
  return discipline ? discipline.name : "";
});

const formatStartTime = computed(() => {
  if (!startTime.value) return "";
  return new Date(startTime.value).toLocaleTimeString("pt-BR", {
    hour: "2-digit",
    minute: "2-digit",
  });
});

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
};

const startSession = async () => {
  if (!selectedDiscipline.value) return;

  try {
    const response = await axios.post(route("study-sessions.start"), {
      discipline_id: selectedDiscipline.value,
    });

    if (response.data.success) {
      activeSession.value = response.data.study_session_id;
      startTime.value = response.data.started_at;
      startTimestamp.value = Date.now(); // Armazena o timestamp de início
      elapsedTime.value = 0;

      // Modifique o timer para usar timestamps
      timer = setInterval(() => {
        const currentTime = Date.now();
        const elapsed = Math.floor((currentTime - startTimestamp.value) / 1000);
        elapsedTime.value = elapsed;
      }, 1000);
    }
  } catch (error) {
    console.error("Error starting study session:", error);
    alert("Ocorreu um erro ao iniciar a sessão de estudo.");
  }
};

const endSession = async () => {
  if (!activeSession.value) return;

  try {
    const response = await axios.post(
      route("study-sessions.end", { studySessionId: activeSession.value }),
      {
        notes: notes.value,
      }
    );

    if (response.data.success) {
      clearInterval(timer);
      timer = null;

      // Emit event to parent component
      emit("sessionEnded", response.data.study_session);

      // Reset state
      activeSession.value = null;
      startTime.value = null;
      elapsedTime.value = 0;
      notes.value = "";
      selectedDiscipline.value = "";
      selectedDisciplineGroup.value = "";
    }
  } catch (error) {
    console.error("Error ending study session:", error);
    alert("Ocorreu um erro ao finalizar a sessão de estudo.");
  }
};

// Clean up timer when component is unmounted
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.study-session-timer {
  @apply p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-100 dark:border-gray-700;
}

.timer-display {
  @apply py-4 px-6 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-100 dark:border-indigo-800;
}

.session-info {
  @apply transition-all duration-200;
}
</style>
