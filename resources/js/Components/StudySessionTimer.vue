<template>
  <div class="study-session-timer">
    <div v-if="!activeSession" class="start-session">
      <!-- Discipline Group Selection -->
      <div class="mb-4">
        <label
          for="disciplineGroup"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Selecione um grupo de disciplinas
        </label>
        <select
          id="disciplineGroup"
          v-model="selectedDisciplineGroup"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
        >
          <option value="" disabled>Selecione um grupo</option>
          <option
            v-for="group in disciplineGroups"
            :key="group.id"
            :value="group.id"
          >
            {{ group.name }}
          </option>
        </select>
      </div>

      <!-- Discipline Selection -->
      <div class="mb-4" v-if="selectedDisciplineGroup">
        <label
          for="discipline"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Selecione uma disciplina
        </label>
        <select
          id="discipline"
          v-model="selectedDiscipline"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
        >
          <option value="" disabled>Selecione uma disciplina</option>
          <option
            v-for="discipline in filteredDisciplines"
            :key="discipline.id"
            :value="discipline.id"
          >
            {{ discipline.name }}
          </option>
        </select>
      </div>

      <!-- Pomodoro Configuration -->
      <div class="mb-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
        <div class="mb-3 flex items-center">
          <input
            id="pomodoroMode"
            v-model="pomodoroMode"
            type="checkbox"
            class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
          />
          <label
            for="pomodoroMode"
            class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            <i class="ri-timer-line mr-1"></i>
            Usar Técnica Pomodoro
          </label>
        </div>

        <div v-if="pomodoroMode" class="grid grid-cols-2 gap-4">
          <div>
            <label
              class="block text-xs font-medium text-gray-600 dark:text-gray-400"
            >
              Tempo de Estudo (min)
            </label>
            <input
              v-model.number="studyDuration"
              type="number"
              min="5"
              max="120"
              class="mt-1 block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label
              class="block text-xs font-medium text-gray-600 dark:text-gray-400"
            >
              Tempo de Descanso (min)
            </label>
            <input
              v-model.number="breakDuration"
              type="number"
              min="1"
              max="60"
              class="mt-1 block w-full rounded-md border-gray-300 text-sm shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      </div>

      <button
        @click="startSession"
        :disabled="!selectedDiscipline"
        class="inline-flex w-full items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <i class="ri-play-circle-line mr-2"></i>
        {{ pomodoroMode ? "Iniciar Pomodoro" : "Iniciar Sessão de Estudo" }}
      </button>
    </div>

    <div v-else class="active-session">
      <!-- Study Session Info -->
      <div
        class="session-info mb-4 p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-200 dark:border-indigo-800"
      >
        <h3
          class="text-lg font-semibold text-indigo-900 dark:text-indigo-100 mb-2"
        >
          <i class="ri-book-open-line mr-2"></i>
          {{
            isPomodoro
              ? isBreak
                ? "Intervalo Ativo"
                : "Pomodoro Ativo"
              : "Sessão de Estudo Ativa"
          }}
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
          <div class="flex items-center">
            <i
              class="ri-folder-line mr-2 text-indigo-600 dark:text-indigo-400"
            ></i>
            <span class="text-gray-700 dark:text-gray-300">
              <strong>Grupo:</strong> {{ currentGroupName }}
            </span>
          </div>
          <div class="flex items-center">
            <i
              class="ri-book-line mr-2 text-indigo-600 dark:text-indigo-400"
            ></i>
            <span class="text-gray-700 dark:text-gray-300">
              <strong>Disciplina:</strong> {{ currentDisciplineName }}
            </span>
          </div>
        </div>

        <!-- Pomodoro Info -->
        <div
          v-if="isPomodoro"
          class="mt-3 pt-3 border-t border-indigo-200 dark:border-indigo-700"
        >
          <div class="flex items-center justify-between text-sm">
            <span class="text-gray-700 dark:text-gray-300">
              <i class="ri-trophy-line mr-1 text-yellow-500"></i>
              <strong>Pomodoros Completados:</strong> {{ completedPomodoros }}
            </span>
            <span class="text-gray-700 dark:text-gray-300">
              <i class="ri-timer-line mr-1 text-indigo-500"></i>
              <strong>{{ isBreak ? "Descanso" : "Estudo" }}:</strong>
              {{ isBreak ? breakDuration : studyDuration }}min
            </span>
          </div>
        </div>
      </div>

      <!-- Timer Display -->
      <div class="timer-display mb-4 text-center">
        <div class="mb-2">
          <div
            class="text-4xl font-bold"
            :class="isBreak ? 'text-green-600' : 'text-indigo-600'"
          >
            {{ formatTime(displayTime) }}
          </div>
          <div v-if="isPomodoro" class="text-sm text-gray-500">
            {{
              isBreak
                ? "Tempo de descanso restante"
                : "Tempo de estudo restante"
            }}
          </div>
        </div>

        <!-- Progress Bar for Pomodoro -->
        <div v-if="isPomodoro" class="mb-3">
          <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
            <div
              class="h-2 rounded-full transition-all duration-1000"
              :class="isBreak ? 'bg-green-500' : 'bg-indigo-600'"
              :style="{ width: progressPercentage + '%' }"
            ></div>
          </div>
        </div>

        <div class="text-sm text-gray-500">
          Iniciado às {{ formatStartTime }}
          <span v-if="isPaused" class="ml-2 text-yellow-600 font-medium">
            <i class="ri-pause-circle-line mr-1"></i>PAUSADO
          </span>
        </div>
      </div>

      <!-- Control Buttons -->
      <div class="flex flex-col space-y-3 mb-4">
        <div class="flex space-x-2">
          <button
            @click="togglePause"
            class="flex-1 inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2"
            :class="
              isPaused
                ? 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                : 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500'
            "
          >
            <i
              :class="isPaused ? 'ri-play-circle-line' : 'ri-pause-circle-line'"
              class="mr-2"
            ></i>
            {{ isPaused ? "Retomar" : "Pausar" }}
          </button>

          <button
            v-if="isPomodoro && !isBreak && timeRemaining <= 0"
            @click="startBreak"
            class="flex-1 inline-flex items-center justify-center rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            <i class="ri-coffee-line mr-2"></i>
            Iniciar Descanso
          </button>

          <button
            v-if="isPomodoro && isBreak && timeRemaining <= 0"
            @click="startNextPomodoro"
            class="flex-1 inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            <i class="ri-play-circle-line mr-2"></i>
            Próximo Pomodoro
          </button>
        </div>
      </div>

      <!-- Notes -->
      <div class="mb-4">
        <label
          for="notes"
          class="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Anotações (opcional)
        </label>
        <textarea
          id="notes"
          v-model="notes"
          rows="3"
          class="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
          placeholder="Adicione anotações sobre o que você estudou nesta sessão..."
        ></textarea>
      </div>

      <!-- End Session Button -->
      <button
        @click="endSession"
        class="inline-flex w-full items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
      >
        <i class="ri-stop-circle-line mr-2"></i>
        Finalizar Sessão
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, watch } from "vue";
import axios from "axios";

const props = defineProps({
  disciplines: {
    type: Array,
    required: true,
  },
  disciplineGroups: {
    type: Array,
    required: true,
  },
});

const emit = defineEmits(["sessionEnded"]);

// Basic session state
const selectedDisciplineGroup = ref("");
const selectedDiscipline = ref("");
const activeSession = ref(null);
const startTime = ref(null);
const elapsedTime = ref(0);
const notes = ref("");
const startTimestamp = ref(null);

// Pomodoro configuration
const pomodoroMode = ref(false);
const studyDuration = ref(25); // minutes
const breakDuration = ref(5); // minutes

// Pomodoro session state
const isPomodoro = ref(false);
const isBreak = ref(false);
const isPaused = ref(false);
const completedPomodoros = ref(0);
const timeRemaining = ref(0);
const currentCycleDuration = ref(0);

let timer = null;

// Computed property to filter disciplines based on selected group
const filteredDisciplines = computed(() => {
  if (!selectedDisciplineGroup.value) return [];

  // Find the selected group
  const group = props.disciplineGroups.find(
    (group) => group.id === selectedDisciplineGroup.value
  );

  // Return disciplines from that group
  return group ? group.disciplines : [];
});

// Reset discipline selection when group changes
watch(selectedDisciplineGroup, () => {
  selectedDiscipline.value = "";
});

// Get current group name
const currentGroupName = computed(() => {
  if (!selectedDisciplineGroup.value) return "";
  const group = props.disciplineGroups.find(
    (group) => group.id === selectedDisciplineGroup.value
  );
  return group ? group.name : "";
});

// Get current discipline name
const currentDisciplineName = computed(() => {
  if (!selectedDiscipline.value) return "";
  const discipline = filteredDisciplines.value.find(
    (discipline) => discipline.id === selectedDiscipline.value
  );
  return discipline ? discipline.name : "";
});

const formatStartTime = computed(() => {
  if (!startTime.value) return "";
  return new Date(startTime.value).toLocaleTimeString("pt-BR", {
    hour: "2-digit",
    minute: "2-digit",
  });
});

// Display time - either elapsed time or remaining time for Pomodoro
const displayTime = computed(() => {
  if (isPomodoro.value) {
    return Math.max(0, timeRemaining.value);
  }
  return elapsedTime.value;
});

// Progress percentage for Pomodoro progress bar
const progressPercentage = computed(() => {
  if (!isPomodoro.value || currentCycleDuration.value === 0) return 0;
  const elapsed = currentCycleDuration.value - timeRemaining.value;
  return Math.min(100, (elapsed / currentCycleDuration.value) * 100);
});

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;
  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// Play notification sound
const playNotificationSound = () => {
  try {
    // Create a simple beep sound using Web Audio API
    const audioContext = new (window.AudioContext ||
      window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = 800; // Frequency in Hz
    oscillator.type = "sine";

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(
      0.01,
      audioContext.currentTime + 0.5
    );

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.5);
  } catch (error) {
    console.log("Could not play notification sound:", error);
  }
};

const startSession = async () => {
  if (!selectedDiscipline.value) return;

  try {
    const response = await axios.post(route("study-sessions.start"), {
      discipline_id: selectedDiscipline.value,
      is_pomodoro: pomodoroMode.value,
      study_duration_minutes: pomodoroMode.value ? studyDuration.value : null,
      break_duration_minutes: pomodoroMode.value ? breakDuration.value : null,
    });

    if (response.data.success) {
      activeSession.value = response.data.study_session_id;
      startTime.value = response.data.started_at;
      startTimestamp.value = Date.now();
      elapsedTime.value = 0;

      // Set Pomodoro state
      isPomodoro.value = response.data.is_pomodoro || false;
      isBreak.value = false;
      isPaused.value = false;
      completedPomodoros.value = 0;

      if (isPomodoro.value) {
        currentCycleDuration.value = studyDuration.value * 60; // Convert to seconds
        timeRemaining.value = currentCycleDuration.value;
      }

      // Start the timer
      startTimer();
    }
  } catch (error) {
    console.error("Error starting study session:", error);
    alert("Ocorreu um erro ao iniciar a sessão de estudo.");
  }
};

const startTimer = () => {
  if (timer) clearInterval(timer);

  timer = setInterval(() => {
    if (isPaused.value) return;

    const currentTime = Date.now();
    const elapsed = Math.floor((currentTime - startTimestamp.value) / 1000);
    elapsedTime.value = elapsed;

    if (isPomodoro.value) {
      timeRemaining.value = Math.max(0, currentCycleDuration.value - elapsed);

      // Check if time is up
      if (timeRemaining.value <= 0) {
        playNotificationSound();
        clearInterval(timer);

        if (!isBreak.value) {
          // Study session completed
          completedPomodoros.value++;
        }
      }
    }
  }, 1000);
};

const endSession = async () => {
  if (!activeSession.value) return;

  try {
    const response = await axios.post(
      route("study-sessions.end", { studySessionId: activeSession.value }),
      {
        notes: notes.value,
      }
    );

    if (response.data.success) {
      clearInterval(timer);
      timer = null;

      // Emit event to parent component
      emit("sessionEnded", response.data.study_session);

      // Reset state
      activeSession.value = null;
      startTime.value = null;
      elapsedTime.value = 0;
      notes.value = "";
      selectedDiscipline.value = "";
      selectedDisciplineGroup.value = "";

      // Reset Pomodoro state
      isPomodoro.value = false;
      isBreak.value = false;
      isPaused.value = false;
      completedPomodoros.value = 0;
      timeRemaining.value = 0;
      currentCycleDuration.value = 0;
    }
  } catch (error) {
    console.error("Error ending study session:", error);
    alert("Ocorreu um erro ao finalizar a sessão de estudo.");
  }
};

// Pause/Resume functionality
const togglePause = async () => {
  if (!activeSession.value) return;

  try {
    if (isPaused.value) {
      // Resume
      const response = await axios.post(
        route("study-sessions.resume", { studySessionId: activeSession.value })
      );

      if (response.data.success) {
        isPaused.value = false;
        // Adjust timestamp to account for paused time
        const pausedDuration = response.data.paused_duration_added;
        startTimestamp.value += pausedDuration * 1000;
        startTimer();
      }
    } else {
      // Pause
      const response = await axios.post(
        route("study-sessions.pause", { studySessionId: activeSession.value })
      );

      if (response.data.success) {
        isPaused.value = true;
        clearInterval(timer);
      }
    }
  } catch (error) {
    console.error("Error toggling pause:", error);
    alert("Erro ao pausar/retomar sessão");
  }
};

// Start break after completing a study session
const startBreak = async () => {
  if (!activeSession.value || !isPomodoro.value) return;

  try {
    const response = await axios.post(
      route("study-sessions.complete-pomodoro", {
        studySessionId: activeSession.value,
      })
    );

    if (response.data.success) {
      isBreak.value = true;
      completedPomodoros.value = response.data.completed_pomodoros;
      currentCycleDuration.value = breakDuration.value * 60;
      timeRemaining.value = currentCycleDuration.value;

      // Reset timer for break
      startTimestamp.value = Date.now();
      elapsedTime.value = 0;
      startTimer();
    }
  } catch (error) {
    console.error("Error starting break:", error);
    alert("Erro ao iniciar intervalo");
  }
};

// Start next Pomodoro after break
const startNextPomodoro = async () => {
  if (!activeSession.value || !isPomodoro.value) return;

  try {
    const response = await axios.post(
      route("study-sessions.complete-break", {
        studySessionId: activeSession.value,
      })
    );

    if (response.data.success) {
      isBreak.value = false;
      currentCycleDuration.value = studyDuration.value * 60;
      timeRemaining.value = currentCycleDuration.value;

      // Reset timer for next study session
      startTimestamp.value = Date.now();
      elapsedTime.value = 0;
      startTimer();
    }
  } catch (error) {
    console.error("Error starting next pomodoro:", error);
    alert("Erro ao iniciar próximo pomodoro");
  }
};

// Clean up timer when component is unmounted
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped>
.study-session-timer {
  @apply p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-100 dark:border-gray-700;
}

.timer-display {
  @apply py-4 px-6 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg border border-indigo-100 dark:border-indigo-800;
}

.session-info {
  @apply transition-all duration-200;
}
</style>
