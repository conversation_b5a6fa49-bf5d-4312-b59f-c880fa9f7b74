<script setup>
import { Link } from "@inertiajs/vue3";
import { ref, onMounted } from "vue";
import ApplicationLogo from "@/Components/ApplicationLogo.vue";

const props = defineProps({
  canLogin: {
    type: Boolean,
    default: false,
  },
  canRegister: {
    type: Boolean,
    default: false,
  },
  currentRoute: {
    type: String,
    required: true,
  },
});

const isCurrentRoute = (routeName) => {
  return props.currentRoute === routeName;
};

const mobileMenuOpen = ref(false);

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

const closeMobileMenu = () => {
  mobileMenuOpen.value = false;
};
</script>

<template>
  <header class="py-4 md:py-10">
    <div class="flex items-center justify-between">
      <!-- Logo -->
      <div class="flex items-center gap-2">
        <Link
          :href="route('welcome')"
          class="flex items-center gap-2"
          @click="closeMobileMenu"
        >
          <ApplicationLogo class="h-10 w-auto text-[#4F46E5]" />
          <span class="text-xl md:text-2xl font-bold text-[#4F46E5]"
            >Estudus</span
          >
        </Link>
      </div>

      <!-- Desktop Navigation -->
      <nav class="hidden lg:flex items-center gap-4">
        <Link
          :href="route('welcome')"
          class="rounded-md px-3 py-2 text-sm font-medium transition"
          :class="{
            'text-[#4F46E5] border-b-2 border-[#4F46E5]':
              isCurrentRoute('welcome'),
            'text-gray-700 hover:text-[#4F46E5] dark:text-gray-300 dark:hover:text-white':
              !isCurrentRoute('welcome'),
          }"
        >
          Início
        </Link>
        <Link
          :href="route('university-students')"
          class="rounded-md px-3 py-2 text-sm font-medium transition"
          :class="{
            'text-[#4F46E5] border-b-2 border-[#4F46E5]': isCurrentRoute(
              'university-students'
            ),
            'text-gray-700 hover:text-[#4F46E5] dark:text-gray-300 dark:hover:text-white':
              !isCurrentRoute('university-students'),
          }"
        >
          Universitários
        </Link>
        <Link
          :href="route('exam-students')"
          class="rounded-md px-3 py-2 text-sm font-medium transition"
          :class="{
            'text-[#4F46E5] border-b-2 border-[#4F46E5]':
              isCurrentRoute('exam-students'),
            'text-gray-700 hover:text-[#4F46E5] dark:text-gray-300 dark:hover:text-white':
              !isCurrentRoute('exam-students'),
          }"
        >
          Concursos
        </Link>
        <Link
          :href="route('professionals')"
          class="rounded-md px-3 py-2 text-sm font-medium transition"
          :class="{
            'text-[#4F46E5] border-b-2 border-[#4F46E5]':
              isCurrentRoute('professionals'),
            'text-gray-700 hover:text-[#4F46E5] dark:text-gray-300 dark:hover:text-white':
              !isCurrentRoute('professionals'),
          }"
        >
          Profissionais
        </Link>

        <div v-if="canLogin" class="ml-4 flex items-center gap-2">
          <Link
            v-if="$page.props.auth.user"
            :href="route('dashboard')"
            class="rounded-md px-4 py-2.5 text-sm font-medium text-white bg-[#4F46E5] transition hover:bg-[#4338CA] focus:outline-none focus-visible:ring-2 focus-visible:ring-[#4F46E5] focus-visible:ring-offset-2"
          >
            Dashboard
          </Link>

          <template v-else>
            <Link
              :href="route('login')"
              class="rounded-md px-4 py-2.5 text-sm font-medium text-[#4F46E5] transition hover:bg-[#4F46E5]/10 focus:outline-none focus-visible:ring-2 focus-visible:ring-[#4F46E5] focus-visible:ring-offset-2"
            >
              Entrar
            </Link>

            <Link
              v-if="canRegister"
              :href="route('register')"
              class="rounded-md px-4 py-2.5 text-sm font-medium text-white bg-[#4F46E5] transition hover:bg-[#4338CA] focus:outline-none focus-visible:ring-2 focus-visible:ring-[#4F46E5] focus-visible:ring-offset-2"
            >
              Cadastrar
            </Link>
          </template>
        </div>
      </nav>

      <!-- Mobile menu button -->
      <button
        @click="toggleMobileMenu"
        type="button"
        class="lg:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-[#4F46E5] hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#4F46E5] dark:text-gray-300 dark:hover:bg-gray-800 transition-colors"
        :aria-expanded="mobileMenuOpen"
      >
        <span class="sr-only">Abrir menu principal</span>
        <!-- Menu icon -->
        <i v-if="!mobileMenuOpen" class="ri-menu-line text-2xl"></i>
        <!-- Close icon -->
        <i v-else class="ri-close-line text-2xl"></i>
      </button>
    </div>

    <!-- Mobile Navigation Menu -->
    <transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 -translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-2"
    >
      <div
        v-if="mobileMenuOpen"
        class="lg:hidden mt-4 pb-4 border-t border-gray-200 dark:border-gray-700"
      >
        <div class="pt-4 space-y-1">
          <Link
            :href="route('welcome')"
            class="flex items-center rounded-md px-3 py-2 text-base font-medium transition-colors"
            :class="{
              'text-[#4F46E5] bg-[#4F46E5]/10 border-l-4 border-[#4F46E5]':
                isCurrentRoute('welcome'),
              'text-gray-700 hover:text-[#4F46E5] hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800':
                !isCurrentRoute('welcome'),
            }"
            @click="closeMobileMenu"
          >
            <i class="ri-home-line mr-3 text-lg"></i>
            Início
          </Link>

          <Link
            :href="route('university-students')"
            class="flex items-center rounded-md px-3 py-2 text-base font-medium transition-colors"
            :class="{
              'text-[#4F46E5] bg-[#4F46E5]/10 border-l-4 border-[#4F46E5]':
                isCurrentRoute('university-students'),
              'text-gray-700 hover:text-[#4F46E5] hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800':
                !isCurrentRoute('university-students'),
            }"
            @click="closeMobileMenu"
          >
            <i class="ri-school-line mr-3 text-lg"></i>
            Universitários
          </Link>

          <Link
            :href="route('exam-students')"
            class="flex items-center rounded-md px-3 py-2 text-base font-medium transition-colors"
            :class="{
              'text-[#4F46E5] bg-[#4F46E5]/10 border-l-4 border-[#4F46E5]':
                isCurrentRoute('exam-students'),
              'text-gray-700 hover:text-[#4F46E5] hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800':
                !isCurrentRoute('exam-students'),
            }"
            @click="closeMobileMenu"
          >
            <i class="ri-file-text-line mr-3 text-lg"></i>
            Concursos
          </Link>

          <Link
            :href="route('professionals')"
            class="flex items-center rounded-md px-3 py-2 text-base font-medium transition-colors"
            :class="{
              'text-[#4F46E5] bg-[#4F46E5]/10 border-l-4 border-[#4F46E5]':
                isCurrentRoute('professionals'),
              'text-gray-700 hover:text-[#4F46E5] hover:bg-gray-50 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-800':
                !isCurrentRoute('professionals'),
            }"
            @click="closeMobileMenu"
          >
            <i class="ri-briefcase-line mr-3 text-lg"></i>
            Profissionais
          </Link>

          <!-- Mobile Login/Register Section -->
          <div
            v-if="canLogin"
            class="pt-4 mt-4 border-t border-gray-200 dark:border-gray-700 space-y-2"
          >
            <Link
              v-if="$page.props.auth.user"
              :href="route('dashboard')"
              class="flex items-center justify-center w-full rounded-md px-4 py-3 text-base font-medium text-white bg-[#4F46E5] transition-colors hover:bg-[#4338CA] focus:outline-none focus-visible:ring-2 focus-visible:ring-[#4F46E5] focus-visible:ring-offset-2"
              @click="closeMobileMenu"
            >
              <i class="ri-dashboard-line mr-2 text-lg"></i>
              Dashboard
            </Link>

            <template v-else>
              <Link
                :href="route('login')"
                class="flex items-center justify-center w-full rounded-md px-4 py-3 text-base font-medium text-[#4F46E5] border border-[#4F46E5] transition-colors hover:bg-[#4F46E5]/10 focus:outline-none focus-visible:ring-2 focus-visible:ring-[#4F46E5] focus-visible:ring-offset-2"
                @click="closeMobileMenu"
              >
                <i class="ri-login-box-line mr-2 text-lg"></i>
                Entrar
              </Link>

              <Link
                v-if="canRegister"
                :href="route('register')"
                class="flex items-center justify-center w-full rounded-md px-4 py-3 text-base font-medium text-white bg-[#4F46E5] transition-colors hover:bg-[#4338CA] focus:outline-none focus-visible:ring-2 focus-visible:ring-[#4F46E5] focus-visible:ring-offset-2"
                @click="closeMobileMenu"
              >
                <i class="ri-user-add-line mr-2 text-lg"></i>
                Cadastrar
              </Link>
            </template>
          </div>
        </div>
      </div>
    </transition>
  </header>
</template>