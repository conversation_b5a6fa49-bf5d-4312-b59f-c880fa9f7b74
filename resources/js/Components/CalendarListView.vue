<template>
  <div class="calendar-list-view">
    <!-- Navigation Header -->
    <div class="flex justify-between items-center mb-4 sm:mb-6">
      <button
        @click="prevMonth"
        class="flex items-center justify-center px-3 py-2 sm:px-4 bg-gray-200 hover:bg-gray-300 rounded-md text-sm sm:text-base font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white"
      >
        <i class="ri-arrow-left-line sm:mr-1"></i>
        <span class="hidden sm:inline">Anterior</span>
      </button>
      <h2
        class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white text-center"
      >
        {{ currentMonthName }} {{ currentYear }}
      </h2>
      <button
        @click="nextMonth"
        class="flex items-center justify-center px-3 py-2 sm:px-4 bg-gray-200 hover:bg-gray-300 rounded-md text-sm sm:text-base font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white"
      >
        <span class="hidden sm:inline">Próximo</span>
        <i class="ri-arrow-right-line sm:ml-1"></i>
      </button>
    </div>

    <!-- Days List -->
    <div class="space-y-3 sm:space-y-4">
      <div
        v-for="day in daysWithEvents"
        :key="day.date"
        class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
      >
        <!-- Day Header -->
        <div
          class="px-4 py-3 border-b border-gray-200 dark:border-gray-700"
          :class="{
            'bg-blue-50 dark:bg-blue-900/30': day.isToday,
            'bg-gray-50 dark:bg-gray-700/50': !day.isToday,
          }"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <span
                class="text-lg sm:text-xl font-bold"
                :class="{
                  'text-blue-600 dark:text-blue-400': day.isToday,
                  'text-gray-900 dark:text-white': !day.isToday,
                }"
              >
                {{ day.day }}
              </span>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                <span class="font-medium">{{ day.dayName }}</span>
                <span class="hidden sm:inline">, {{ day.fullDate }}</span>
              </div>
            </div>
            <div v-if="day.isToday" class="flex items-center">
              <span
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300"
              >
                <i class="ri-calendar-check-line mr-1"></i>
                Hoje
              </span>
            </div>
          </div>
        </div>

        <!-- Events -->
        <div class="p-4">
          <div v-if="!day.events || (!day.events.lessons?.length && !day.events.revisions?.length)" class="text-center py-4">
            <i class="ri-calendar-line text-2xl text-gray-400 dark:text-gray-500 mb-2"></i>
            <p class="text-sm text-gray-500 dark:text-gray-400">Nenhum evento agendado</p>
          </div>

          <div v-else class="space-y-3">
            <!-- Lessons -->
            <div v-if="day.events.lessons && day.events.lessons.length > 0">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="ri-book-open-line mr-2 text-green-600 dark:text-green-400"></i>
                Aulas ({{ day.events.lessons.length }})
              </h4>
              <div class="space-y-2">
                <div
                  v-for="lesson in day.events.lessons"
                  :key="'lesson-' + lesson.id"
                  class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border-l-4 border-green-500 cursor-pointer hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
                  @click="goToLesson(lesson.id)"
                >
                  <div class="flex-1 min-w-0">
                    <h5 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {{ lesson.title }}
                    </h5>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                      {{ lesson.discipline }}
                    </p>
                  </div>
                  <div class="flex items-center ml-3">
                    <i class="ri-arrow-right-s-line text-gray-400 dark:text-gray-500"></i>
                  </div>
                </div>
              </div>
            </div>

            <!-- Revisions -->
            <div v-if="day.events.revisions && day.events.revisions.length > 0">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                <i class="ri-refresh-line mr-2 text-indigo-600 dark:text-indigo-400"></i>
                Revisões ({{ day.events.revisions.length }})
              </h4>
              <div class="space-y-2">
                <div
                  v-for="revision in day.events.revisions"
                  :key="'revision-' + revision.id"
                  class="flex items-center justify-between p-3 rounded-lg border-l-4 cursor-pointer transition-colors"
                  :class="getRevisionClasses(revision, day.date)"
                  @click="goToRevision(revision.id)"
                >
                  <div class="flex-1 min-w-0">
                    <h5 class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {{ revision.lesson_title }}
                    </h5>
                    <div class="flex items-center space-x-2 mt-1">
                      <p class="text-xs text-gray-600 dark:text-gray-400">
                        {{ revision.discipline }}
                      </p>
                      <span class="text-xs text-gray-400">•</span>
                      <span class="text-xs font-medium" :class="getRevisionTypeColor(revision)">
                        {{ getRevisionTypeLabel(revision.type) }}
                      </span>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2 ml-3">
                    <div v-if="revision.is_completed" class="flex items-center">
                      <i class="ri-check-line text-green-600 dark:text-green-400"></i>
                    </div>
                    <div v-else-if="isOverdue(revision, day.date)" class="flex items-center">
                      <i class="ri-time-line text-red-600 dark:text-red-400"></i>
                    </div>
                    <div v-else-if="isToday(day.date)" class="flex items-center">
                      <i class="ri-calendar-check-line text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <i class="ri-arrow-right-s-line text-gray-400 dark:text-gray-500"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="daysWithEvents.length === 0" class="text-center py-12">
      <i class="ri-calendar-line text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Nenhum evento neste mês
      </h3>
      <p class="text-gray-600 dark:text-gray-400">
        Não há aulas ou revisões agendadas para {{ currentMonthName }} {{ currentYear }}.
      </p>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { router } from "@inertiajs/vue3";

const props = defineProps({
  calendarData: {
    type: Object,
    required: true,
  },
  month: {
    type: Number,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
});

const currentMonth = computed(() => props.month);
const currentYear = computed(() => props.year);

const currentMonthName = computed(() => {
  const monthNames = [
    "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho",
    "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"
  ];
  return monthNames[currentMonth.value - 1];
});

const daysWithEvents = computed(() => {
  const days = [];
  const today = new Date();
  const todayStr = formatDate(today);

  // Get all dates that have events
  const eventDates = Object.keys(props.calendarData).sort();

  eventDates.forEach(dateStr => {
    const date = new Date(dateStr);
    const dayNames = ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"];
    
    days.push({
      date: dateStr,
      day: date.getDate(),
      dayName: dayNames[date.getDay()],
      fullDate: date.toLocaleDateString('pt-BR'),
      isToday: dateStr === todayStr,
      events: props.calendarData[dateStr],
    });
  });

  return days;
});

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const isToday = (dateStr) => {
  const today = new Date();
  return dateStr === formatDate(today);
};

const isOverdue = (revision, dateStr) => {
  if (revision.is_completed) return false;
  const today = new Date();
  const revisionDate = new Date(dateStr);
  return revisionDate < today;
};

const getRevisionClasses = (revision, dateStr) => {
  if (revision.is_completed) {
    return 'bg-green-50 dark:bg-green-900/20 border-green-500 hover:bg-green-100 dark:hover:bg-green-900/30';
  } else if (isOverdue(revision, dateStr)) {
    return 'bg-red-50 dark:bg-red-900/20 border-red-500 hover:bg-red-100 dark:hover:bg-red-900/30';
  } else if (isToday(dateStr)) {
    return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-400 hover:bg-yellow-100 dark:hover:bg-yellow-900/30';
  } else {
    return 'bg-gray-50 dark:bg-gray-700/50 border-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700';
  }
};

const getRevisionTypeColor = (revision) => {
  const colors = {
    'first': 'text-blue-600 dark:text-blue-400',
    'second': 'text-purple-600 dark:text-purple-400',
    'third': 'text-orange-600 dark:text-orange-400',
    'fourth': 'text-red-600 dark:text-red-400',
    'recurring': 'text-green-600 dark:text-green-400',
    'weekly': 'text-indigo-600 dark:text-indigo-400',
  };
  return colors[revision.type] || 'text-gray-600 dark:text-gray-400';
};

const getRevisionTypeLabel = (type) => {
  const labels = {
    'first': '1ª Revisão',
    'second': '2ª Revisão',
    'third': '3ª Revisão',
    'fourth': '4ª Revisão',
    'recurring': 'Revisão Recorrente',
    'weekly': 'Revisão Semanal',
  };
  return labels[type] || type;
};

const prevMonth = () => {
  let newMonth = currentMonth.value;
  let newYear = currentYear.value;

  if (newMonth === 1) {
    newMonth = 12;
    newYear--;
  } else {
    newMonth--;
  }

  router.get("/calendar", {
    month: newMonth,
    year: newYear,
  }, {
    preserveState: true,
    replace: true,
  });
};

const nextMonth = () => {
  let newMonth = currentMonth.value;
  let newYear = currentYear.value;

  if (newMonth === 12) {
    newMonth = 1;
    newYear++;
  } else {
    newMonth++;
  }

  router.get("/calendar", {
    month: newMonth,
    year: newYear,
  }, {
    preserveState: true,
    replace: true,
  });
};

const goToLesson = (lessonId) => {
  router.visit(`/lessons/${lessonId}`);
};

const goToRevision = (revisionId) => {
  router.visit(`/revisions/${revisionId}`);
};
</script>
