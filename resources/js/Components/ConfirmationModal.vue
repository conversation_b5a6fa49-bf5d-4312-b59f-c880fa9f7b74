<template>
  <Modal :show="show" @close="$emit('close')">
    <div class="p-6">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        {{ title }}
      </h2>
      <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
        {{ message }}
      </p>
      <div class="mt-6 flex justify-end space-x-3">
        <SecondaryButton @click="$emit('close')">
          {{ cancelButtonText }}
        </SecondaryButton>
        <DangerButton
          @click="$emit('confirm')"
          :class="{ 'opacity-25': processing }"
          :disabled="processing"
        >
          {{ confirmButtonText }}
        </DangerButton>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import { ref } from "vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import DangerButton from "@/Components/DangerButton.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "Confirmar Ação",
  },
  message: {
    type: String,
    default: "Tem certeza que deseja realizar esta ação?",
  },
  confirmButtonText: {
    type: String,
    default: "Confirmar",
  },
  cancelButtonText: {
    type: String,
    default: "Cancelar",
  },
  processing: {
    type: Boolean,
    default: false,
  },
});

defineEmits(["close", "confirm"]);
</script>
