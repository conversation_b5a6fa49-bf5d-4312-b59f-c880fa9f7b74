<template>
  <div>
    <!-- Add Note Form -->
    <div v-if="canEdit" class="mb-6">
      <form @submit.prevent="addNote">
        <div class="mb-4">
          <SimpleMarkdownEditor
            v-model="newNote"
            :rows="5"
            placeholder="Digite sua anotação usando Markdown..."
          />
        </div>
        <div class="flex justify-end">
          <button
            type="submit"
            class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
            :disabled="!newNote.trim()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="mr-1.5 h-4 w-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
            Adicionar Anotação
          </button>
        </div>
      </form>
    </div>

    <!-- Notes List -->
    <div v-if="notes.length > 0" class="space-y-4">
      <div
        v-for="note in notes"
        :key="note.id"
        class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-zinc-800"
      >
        <!-- Note Content -->
        <div v-if="editingNote && editingNote.id === note.id">
          <form @submit.prevent="updateNote">
            <div class="mb-4">
              <SimpleMarkdownEditor
                v-model="editingNote.content"
                :rows="5"
                placeholder="Digite sua anotação usando Markdown..."
              />
            </div>
            <div class="flex justify-end space-x-2">
              <button
                type="button"
                @click="cancelEdit"
                class="flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 dark:hover:bg-zinc-700"
              >
                Cancelar
              </button>
              <button
                type="submit"
                class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                :disabled="!editingNote.content.trim()"
              >
                Salvar
              </button>
            </div>
          </form>
        </div>
        <div v-else>
          <MarkdownRenderer :content="note.content" />

          <!-- Note Actions -->
          <div v-if="canEdit" class="mt-4 flex justify-end space-x-2">
            <button
              @click="startEdit(note)"
              class="flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 dark:hover:bg-zinc-700"
            >
              <i class="ri-edit-line mr-1"></i>
              Editar
            </button>
            <button
              @click="confirmDeleteNote(note)"
              class="flex items-center rounded-lg border border-red-300 bg-white px-3 py-1 text-xs font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-800 dark:bg-zinc-800 dark:text-red-400 dark:hover:bg-red-900/30"
            >
              <i class="ri-delete-bin-line mr-1"></i>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div
      v-else
      class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 text-center dark:border-gray-700 dark:bg-zinc-800/50"
    >
      <i
        class="ri-sticky-note-line mb-4 text-5xl text-gray-400 dark:text-gray-500"
      ></i>
      <p class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">
        Nenhuma anotação encontrada
      </p>
      <p v-if="canEdit" class="mb-4 text-xs text-gray-500 dark:text-gray-500">
        Adicione anotações para organizar seus estudos
      </p>
    </div>

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :show="showDeleteModal"
      title="Excluir Anotação"
      message="Tem certeza que deseja excluir esta anotação? Esta ação não pode ser desfeita."
      confirm-button-text="Excluir"
      @confirm="deleteNote"
      @close="showDeleteModal = false"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { router } from "@inertiajs/vue3";
import SimpleMarkdownEditor from "@/Components/SimpleMarkdownEditor.vue";
import MarkdownRenderer from "@/Components/MarkdownRenderer.vue";
import ConfirmationModal from "@/Components/ConfirmationModal.vue";

const props = defineProps({
  notes: {
    type: Array,
    default: () => [],
  },
  lessonId: {
    type: Number,
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
});

const newNote = ref("");
const editingNote = ref(null);
const showDeleteModal = ref(false);
const noteToDelete = ref(null);

const addNote = () => {
  if (!newNote.value.trim()) return;

  router.post(
    route("notes.store"),
    {
      content: newNote.value,
      lesson_id: props.lessonId,
    },
    {
      onSuccess: () => {
        newNote.value = "";
      },
    }
  );
};

const startEdit = (note) => {
  editingNote.value = {
    id: note.id,
    content: note.content,
  };
};

const cancelEdit = () => {
  editingNote.value = null;
};

const updateNote = () => {
  if (!editingNote.value || !editingNote.value.content.trim()) return;

  router.put(
    route("notes.update", editingNote.value.id),
    {
      content: editingNote.value.content,
    },
    {
      onSuccess: () => {
        editingNote.value = null;
      },
    }
  );
};

const confirmDeleteNote = (note) => {
  noteToDelete.value = note;
  showDeleteModal.value = true;
};

const deleteNote = () => {
  if (!noteToDelete.value) return;

  router.delete(route("notes.destroy", noteToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      noteToDelete.value = null;
    },
  });
};
</script>
