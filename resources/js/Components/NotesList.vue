<template>
  <div>
    <!-- Add Note Form -->
    <div v-if="canEdit" class="mb-6">
      <form @submit.prevent="addNote">
        <div class="mb-4">
          <SimpleMarkdownEditor
            v-model="newNote"
            :rows="5"
            placeholder="Digite sua anotação usando Markdown..."
          />
        </div>
        <div class="flex justify-end">
          <button
            type="submit"
            class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
            :disabled="!newNote.trim()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="mr-1.5 h-4 w-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
            Adicionar Anotação
          </button>
        </div>
      </form>
    </div>

    <!-- Notes List -->
    <div v-if="notes.length > 0" class="space-y-4">
      <div
        v-for="note in notes"
        :key="note.id"
        class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-zinc-800"
      >
        <!-- Note Content -->
        <div v-if="editingNote && editingNote.id === note.id">
          <form @submit.prevent="updateNote">
            <div class="mb-4">
              <SimpleMarkdownEditor
                v-model="editingNote.content"
                :rows="5"
                placeholder="Digite sua anotação usando Markdown..."
              />
            </div>
            <div class="flex justify-end space-x-2">
              <button
                type="button"
                @click="cancelEdit"
                class="flex items-center rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 dark:hover:bg-zinc-700"
              >
                Cancelar
              </button>
              <button
                type="submit"
                class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                :disabled="!editingNote.content.trim()"
              >
                Salvar
              </button>
            </div>
          </form>
        </div>
        <div v-else>
          <MarkdownRenderer :content="note.content" />

          <!-- Note Actions -->
          <div v-if="canEdit" class="mt-4 flex justify-end space-x-2">
            <button
              @click="startEdit(note)"
              class="flex items-center rounded-lg border border-gray-300 bg-white px-3 py-1 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 dark:hover:bg-zinc-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-1 h-3.5 w-3.5"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                />
              </svg>
              Editar
            </button>
            <button
              @click="confirmDeleteNote(note)"
              class="flex items-center rounded-lg border border-red-300 bg-white px-3 py-1 text-xs font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:border-red-800 dark:bg-zinc-800 dark:text-red-400 dark:hover:bg-red-900/30"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-1 h-3.5 w-3.5"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                />
              </svg>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div
      v-else
      class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 text-center dark:border-gray-700 dark:bg-zinc-800/50"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="mb-4 h-12 w-12 text-gray-400 dark:text-gray-500"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
        />
      </svg>
      <p class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400">
        Nenhuma anotação encontrada
      </p>
      <p v-if="canEdit" class="mb-4 text-xs text-gray-500 dark:text-gray-500">
        Adicione anotações para organizar seus estudos
      </p>
    </div>

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :show="showDeleteModal"
      title="Excluir Anotação"
      message="Tem certeza que deseja excluir esta anotação? Esta ação não pode ser desfeita."
      confirm-button-text="Excluir"
      @confirm="deleteNote"
      @close="showDeleteModal = false"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { router } from "@inertiajs/vue3";
import SimpleMarkdownEditor from "@/Components/SimpleMarkdownEditor.vue";
import MarkdownRenderer from "@/Components/MarkdownRenderer.vue";
import ConfirmationModal from "@/Components/ConfirmationModal.vue";

const props = defineProps({
  notes: {
    type: Array,
    default: () => [],
  },
  lessonId: {
    type: Number,
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: true,
  },
});

const newNote = ref("");
const editingNote = ref(null);
const showDeleteModal = ref(false);
const noteToDelete = ref(null);

const addNote = () => {
  if (!newNote.value.trim()) return;

  router.post(
    route("notes.store"),
    {
      content: newNote.value,
      lesson_id: props.lessonId,
    },
    {
      onSuccess: () => {
        newNote.value = "";
      },
    }
  );
};

const startEdit = (note) => {
  editingNote.value = {
    id: note.id,
    content: note.content,
  };
};

const cancelEdit = () => {
  editingNote.value = null;
};

const updateNote = () => {
  if (!editingNote.value || !editingNote.value.content.trim()) return;

  router.put(
    route("notes.update", editingNote.value.id),
    {
      content: editingNote.value.content,
    },
    {
      onSuccess: () => {
        editingNote.value = null;
      },
    }
  );
};

const confirmDeleteNote = (note) => {
  noteToDelete.value = note;
  showDeleteModal.value = true;
};

const deleteNote = () => {
  if (!noteToDelete.value) return;

  router.delete(route("notes.destroy", noteToDelete.value.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
      noteToDelete.value = null;
    },
  });
};
</script>
