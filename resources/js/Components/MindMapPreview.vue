<template>
  <div class="mind-map-preview">
    <div class="flex justify-end mb-2">
      <button
        @click="toggleFullscreen"
        class="px-2 py-1 text-xs bg-indigo-600 text-white rounded-md flex items-center"
      >
        <span v-if="!isFullscreen">Tela Inteira</span>
        <span v-else>Sair da Tela Inteira</span>
      </button>
    </div>

    <!-- Mensagem flutuante ESC -->
    <div
      v-if="isFullscreen"
      class="fixed top-4 right-4 z-50 bg-yellow-100 text-yellow-800 text-sm px-4 py-2 rounded shadow"
    >
      <strong @click="isFullscreen = false">FECHAR</strong>
    </div>

    <div
      ref="mindMapContainer"
      class="mind-map-viewer"
      :class="{ fullscreen: isFullscreen }"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";

const props = defineProps({
  data: {
    type: [Object, String],
    required: true,
  },
});

const mindMapContainer = ref(null);
let markmap = null;
const transformer = new Transformer();
const isFullscreen = ref(false);
let lastRenderedContent = null;
let isRendering = ref(false);
let renderTimeout = null;

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  // Forçar re-renderização apenas quando mudamos para fullscreen
  nextTick(() => {
    if (markmap && mindMapContainer.value) {
      // Apenas redimensionar o SVG existente
      const svg = mindMapContainer.value.querySelector("svg");
      if (svg) {
        svg.style.height = isFullscreen.value ? "calc(100vh - 60px)" : "400px";
        markmap.fit();
      }
    }
  });
};

const handleEscKey = (event) => {
  if (event.key === "Escape" && isFullscreen.value) {
    isFullscreen.value = false;
    nextTick(() => {
      if (markmap && mindMapContainer.value) {
        const svg = mindMapContainer.value.querySelector("svg");
        if (svg) {
          svg.style.height = "400px";
          markmap.fit();
        }
      }
    });
  }
};

const convertOldFormatToMarkdown = (oldData) => {
  let markdown = "";

  const nodeIds = new Set(oldData.nodes.map((node) => node.id));
  const targetIds = new Set(oldData.links.map((link) => link.target));
  const rootNodeIds = [...nodeIds].filter((id) => !targetIds.has(id));

  const nodeMap = {};
  oldData.nodes.forEach((node) => (nodeMap[node.id] = node));

  const childrenMap = {};
  oldData.links.forEach((link) => {
    if (!childrenMap[link.source]) childrenMap[link.source] = [];
    childrenMap[link.source].push(link.target);
  });

  const buildMarkdown = (nodeId, depth) => {
    const node = nodeMap[nodeId];
    if (!node) return "";
    const prefix = "#".repeat(Math.min(depth, 6));
    let result = `${prefix} ${node.name || "Item"}\n`;
    if (childrenMap[nodeId]) {
      childrenMap[nodeId].forEach((childId) => {
        result += buildMarkdown(childId, depth + 1);
      });
    }
    return result;
  };

  rootNodeIds.forEach((rootId) => {
    markdown += buildMarkdown(rootId, 1);
  });

  return markdown || "# Mapa Mental\n## Exemplo\n### Sub-item";
};

const getContentFromData = () => {
  let content = "";
  if (typeof props.data === "string") {
    content = props.data;
  } else if (typeof props.data === "object") {
    if (props.data.content) {
      content = props.data.content;
    } else if (props.data.nodes && props.data.links) {
      content = convertOldFormatToMarkdown(props.data);
    }
  }
  return content.includes("#")
    ? content
    : "# Mapa Mental\n## Exemplo\n### Sub-item";
};

const renderMindMap = async () => {
  if (!mindMapContainer.value || isRendering.value) return;

  const content = getContentFromData();

  // Evitar re-renderização se o conteúdo não mudou
  if (content === lastRenderedContent && markmap) {
    return;
  }

  isRendering.value = true;
  lastRenderedContent = content;

  try {
    mindMapContainer.value.innerHTML = "";

    const { root } = transformer.transform(content);

    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.style.width = "100%";
    svg.style.height = isFullscreen.value ? "calc(100vh - 60px)" : "400px";
    svg.style.display = "block";

    mindMapContainer.value.appendChild(svg);

    // Destruir o markmap anterior se existir
    if (markmap) {
      markmap.destroy?.();
    }

    markmap = Markmap.create(svg, null, root);
  } catch (error) {
    console.error("Erro ao renderizar mapa mental:", error);
  } finally {
    isRendering.value = false;
  }
};

onMounted(() => {
  renderMindMap();
  window.addEventListener("keyup", handleEscKey);
});

onUnmounted(() => {
  // Limpar timeout se existir
  if (renderTimeout) {
    clearTimeout(renderTimeout);
    renderTimeout = null;
  }

  // Destruir markmap
  if (markmap) {
    markmap.destroy?.();
    markmap = null;
  }

  window.removeEventListener("keyup", handleEscKey);
});

// Watch apenas mudanças reais no conteúdo com debounce
watch(
  () => getContentFromData(),
  (newContent, oldContent) => {
    if (newContent !== oldContent && !isRendering.value) {
      // Cancelar timeout anterior se existir
      if (renderTimeout) {
        clearTimeout(renderTimeout);
      }

      // Debounce para evitar re-renderizações excessivas
      renderTimeout = setTimeout(() => {
        renderMindMap();
      }, 100);
    }
  },
  { deep: false }
);
</script>

<style scoped>
.mind-map-viewer {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}
.fullscreen {
  position: fixed !important;
  top: 0;
  left: 0;
  z-index: 40;
  width: 100vw;
  height: 100vh;
  background: white;
  padding: 16px;
}
</style>
