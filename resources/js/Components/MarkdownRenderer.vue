<template>
  <div class="markdown-content" v-html="renderedContent"></div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import MarkdownIt from "markdown-it";

const props = defineProps({
  content: {
    type: String,
    required: true,
  },
});

// Create a new markdown-it instance with all the plugins we need
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
});

// We'll use the basic markdown-it functionality without additional plugins
// This avoids issues with requiring modules that might not be available

const renderedContent = computed(() => {
  if (!props.content) return "";

  try {
    // Render the markdown to HTML
    return md.render(props.content);
  } catch (error) {
    console.error("Error rendering markdown:", error);
    return `<p class="text-red-500">Error rendering content</p>`;
  }
});
</script>

<style>
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #4f46e5;
}

.markdown-content h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #4f46e5;
}

.markdown-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #4f46e5;
}

.markdown-content p {
  margin-bottom: 0.75rem;
}

.markdown-content ul,
.markdown-content ol {
  margin-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.markdown-content ul {
  list-style-type: disc;
}

.markdown-content ol {
  list-style-type: decimal;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content code {
  font-family: monospace;
  background-color: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.markdown-content pre {
  background-color: #f1f5f9;
  padding: 0.75rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin-bottom: 0.75rem;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  font-style: italic;
  margin-bottom: 0.75rem;
}

.markdown-content a {
  color: #2563eb;
  text-decoration: underline;
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  margin: 0.75rem 0;
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 0.75rem;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #e2e8f0;
  padding: 0.5rem;
}

.markdown-content table th {
  background-color: #f8fafc;
  font-weight: 600;
}
</style>
