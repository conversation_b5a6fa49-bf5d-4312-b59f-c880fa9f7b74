<template>
  <div class="markdown-editor">
    <div class="mb-2 flex items-center justify-between">
      <div class="flex space-x-2">
        <button
          v-for="(button, index) in toolbarButtons"
          :key="index"
          @click="insertMarkdown(button.action)"
          type="button"
          class="rounded-md border border-gray-300 bg-white px-2 py-1 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 dark:hover:bg-zinc-700"
          :title="button.title"
        >
          <span v-html="button.icon"></span>
        </button>
      </div>
      <div class="flex items-center">
        <button
          @click="togglePreview"
          type="button"
          class="rounded-md border border-gray-300 bg-white px-3 py-1 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 dark:hover:bg-zinc-700"
        >
          {{ showPreview ? "Editar" : "Visualizar" }}
        </button>
      </div>
    </div>

    <div v-if="!showPreview" class="relative">
      <textarea
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
        class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-zinc-800 dark:text-gray-300 sm:text-sm"
        :rows="rows"
        :placeholder="placeholder"
      ></textarea>
    </div>

    <div v-else class="preview-container">
      <div
        class="prose prose-indigo max-w-none rounded-md border border-gray-300 bg-white p-4 dark:prose-invert dark:border-gray-600 dark:bg-zinc-800"
        v-html="renderedMarkdown"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import MarkdownIt from "markdown-it";

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  rows: {
    type: Number,
    default: 6,
  },
  placeholder: {
    type: String,
    default: "Digite seu texto usando Markdown...",
  },
});

defineEmits(["update:modelValue"]);

const showPreview = ref(false);

// Create a new markdown-it instance
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  breaks: true,
});

const togglePreview = () => {
  showPreview.value = !showPreview.value;
};

const renderedMarkdown = computed(() => {
  if (!props.modelValue) return "";
  return md.render(props.modelValue);
});

const toolbarButtons = [
  {
    title: "Negrito",
    icon: "<strong>B</strong>",
    action: {
      prefix: "**",
      suffix: "**",
      placeholder: "texto em negrito",
    },
  },
  {
    title: "Itálico",
    icon: "<em>I</em>",
    action: {
      prefix: "*",
      suffix: "*",
      placeholder: "texto em itálico",
    },
  },
  {
    title: "Título",
    icon: "H",
    action: {
      prefix: "## ",
      suffix: "",
      placeholder: "Título",
    },
  },
  {
    title: "Link",
    icon: "🔗",
    action: {
      prefix: "[",
      suffix: "](url)",
      placeholder: "texto do link",
    },
  },
  {
    title: "Lista",
    icon: "•",
    action: {
      prefix: "- ",
      suffix: "",
      placeholder: "item da lista",
    },
  },
  {
    title: "Lista Numerada",
    icon: "1.",
    action: {
      prefix: "1. ",
      suffix: "",
      placeholder: "item da lista",
    },
  },
  {
    title: "Código",
    icon: "<code>{}</code>",
    action: {
      prefix: "```\n",
      suffix: "\n```",
      placeholder: "código",
    },
  },
];

const insertMarkdown = (action) => {
  const textarea = document.querySelector(".markdown-editor textarea");
  if (!textarea) return;

  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const text = props.modelValue;
  const selectedText = text.substring(start, end);
  const replacement = selectedText || action.placeholder;
  const newText =
    text.substring(0, start) +
    action.prefix +
    replacement +
    action.suffix +
    text.substring(end);

  textarea.focus();
  // Emit the updated value
  const event = new Event("input", { bubbles: true });
  textarea.value = newText;
  textarea.dispatchEvent(event);

  // Set cursor position
  const newCursorPos =
    selectedText.length > 0
      ? start + action.prefix.length + selectedText.length + action.suffix.length
      : start + action.prefix.length + action.placeholder.length;
  
  setTimeout(() => {
    textarea.setSelectionRange(
      selectedText.length > 0 ? start + action.prefix.length : start + action.prefix.length,
      selectedText.length > 0 ? end + action.prefix.length : start + action.prefix.length + action.placeholder.length
    );
  }, 0);
};
</script>

<style>
.markdown-editor .preview-container {
  min-height: 100px;
}

.markdown-editor .preview-container img {
  max-width: 100%;
  height: auto;
}

.markdown-editor .preview-container pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
}

.dark .markdown-editor .preview-container pre {
  background-color: #1f2937;
}
</style>
