<template>
  <div class="questions-section">
    <div class="section-header">
      <h3 class="text-lg font-semibold text-gray-800 mb-2">
        <i class="ri-question-answer-line mr-2 text-indigo-600"></i>
        Questões
      </h3>

      <b>Prompt para a IA: </b><br />Crie uma planilha Excel contendo 10
      questões-chave com base no tema descrito no markdown abaixo. A planilha
      deve ter as seguintes colunas: <br />
      <ul>
        <li>question: o enunciado da questão</li>
        <li>option1 a option6: seis alternativas de resposta</li>
        <li>
          isCorrect: o número correspondente à alternativa correta (de 1 a 6)
        </li>
        <li>
          explanation: uma explicação clara do motivo pelo qual a opção correta
          está certa
        </li>
      </ul>
      <br />

      <p v-if="questions.length === 0" class="text-gray-600 text-sm">
        Nenhuma questão cadastrada para esta lição.
      </p>
      <p v-else class="text-gray-600 text-sm">
        {{ questions.length }}
        {{ questions.length === 1 ? "questão" : "questões" }} disponíveis para
        revisão.
      </p>
    </div>

    <div v-if="questions.length > 0" class="questions-container mt-4 space-y-6">
      <div
        v-for="(question, index) in questions"
        :key="question.id"
        class="question-card"
      >
        <div class="question-header flex justify-between items-center mb-3">
          <h4 class="font-medium text-indigo-700">Questão {{ index + 1 }}</h4>
          <div class="flex space-x-2" v-if="canEdit">
            <button
              @click="editQuestion(question)"
              class="text-gray-500 hover:text-indigo-600 transition-colors"
              title="Editar questão"
            >
              <i class="ri-edit-line"></i>
            </button>
            <button
              @click="confirmDeleteQuestion(question)"
              class="text-gray-500 hover:text-red-600 transition-colors"
              title="Excluir questão"
            >
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>
        </div>

        <div class="question-content">
          <div class="text-gray-800 font-medium mb-4">
            <MarkdownRenderer :content="question.text" />
          </div>

          <div class="alternatives-list space-y-2">
            <div
              v-for="alternative in question.alternatives"
              :key="alternative.id"
              class="alternative-item"
              :class="{
                selected: selectedAnswers[question.id] === alternative.id,
                correct: showResults[question.id] && alternative.is_correct,
                incorrect:
                  showResults[question.id] &&
                  selectedAnswers[question.id] === alternative.id &&
                  !alternative.is_correct,
              }"
              @click="selectAnswer(question.id, alternative.id)"
            >
              <div class="flex items-center">
                <div class="alternative-marker">
                  <div class="marker-inner"></div>
                </div>
                <span class="alternative-text">
                  <MarkdownRenderer :content="alternative.text" />
                </span>
              </div>
              <div v-if="showResults[question.id]" class="result-icon">
                <i
                  v-if="alternative.is_correct"
                  class="ri-check-line text-green-600"
                ></i>
                <i
                  v-else-if="selectedAnswers[question.id] === alternative.id"
                  class="ri-close-line text-red-600"
                ></i>
              </div>
            </div>
          </div>

          <div class="question-actions mt-4 flex justify-between">
            <button
              v-if="!showResults[question.id] && selectedAnswers[question.id]"
              @click="checkAnswer(question)"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
            >
              Conferir
            </button>
            <button
              v-if="showResults[question.id]"
              @click="resetQuestion(question.id)"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
            >
              Tentar novamente
            </button>
          </div>

          <div
            v-if="showResults[question.id] && question.explanation"
            class="explanation mt-4 p-3 bg-blue-50 rounded-md"
          >
            <h5 class="font-medium text-blue-800 mb-1">Explicação:</h5>
            <div class="text-blue-700 text-sm">
              <MarkdownRenderer :content="question.explanation" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="canEdit" class="add-question-section mt-6">
      <div class="flex space-x-4 mb-4">
        <button
          @click="showAddQuestionForm = !showAddQuestionForm"
          class="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors"
        >
          <i class="ri-add-circle-line mr-1"></i>
          {{ showAddQuestionForm ? "Cancelar" : "Adicionar nova questão" }}
        </button>

        <button
          @click="showImportForm = !showImportForm"
          class="flex items-center text-indigo-600 hover:text-indigo-800 transition-colors"
        >
          <i class="ri-file-excel-line mr-1"></i>
          {{ showImportForm ? "Cancelar" : "Importar questões do Excel" }}
        </button>
      </div>

      <!-- Formulário de importação -->
      <div
        v-if="showImportForm"
        class="import-form mb-6 p-4 bg-gray-50 rounded-lg"
      >
        <h4 class="font-medium text-gray-800 mb-4">
          Importar Questões do Excel
        </h4>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">
            O arquivo Excel deve ter as seguintes colunas:
          </p>
          <ul class="text-xs text-gray-600 list-disc pl-5 mb-4">
            <li>
              <strong>question</strong>: Texto da questão (obrigatório, suporta
              Markdown)
            </li>
            <li>
              <strong>option1, option2, ...</strong>: Alternativas (pelo menos 2
              são obrigatórias, suportam Markdown)
            </li>
            <li>
              <strong>isCorrect</strong>: Número da alternativa correta (1 para
              option1, 2 para option2, etc.)
            </li>
            <li>
              <strong>explanation</strong>: Explicação opcional para a resposta
              (suporta Markdown)
            </li>
          </ul>

          <div class="bg-blue-50 p-3 rounded-md mb-4">
            <p class="text-xs text-blue-700">
              <i class="ri-information-line mr-1"></i>
              Exemplo: Uma linha com <strong>question</strong>="Qual a capital
              do Brasil?", <strong>option1</strong>="Rio de Janeiro",
              <strong>option2</strong>="Brasília", <strong>option3</strong>="São
              Paulo", <strong>isCorrect</strong>=2,
              <strong>explanation</strong>="Brasília é a capital do Brasil desde
              1960."
            </p>
            <div class="mt-2">
              <a
                href="/examples/questions_template.csv"
                download
                class="text-xs text-indigo-600 hover:text-indigo-800 flex items-center"
              >
                <i class="ri-download-line mr-1"></i>
                Baixar modelo de arquivo CSV
              </a>
            </div>
          </div>
        </div>

        <form @submit.prevent="importQuestions" enctype="multipart/form-data">
          <div class="mb-4">
            <label
              for="excelFile"
              class="block text-sm font-medium text-gray-700 mb-1"
              >Arquivo Excel</label
            >
            <input
              type="file"
              id="excelFile"
              ref="fileInput"
              accept=".xlsx,.xls,.csv"
              class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
            />
            <p class="mt-1 text-xs text-gray-500">
              Formatos aceitos: .xlsx, .xls, .csv
            </p>
          </div>

          <div class="flex justify-end">
            <button
              type="submit"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
              :disabled="isImporting"
            >
              {{ isImporting ? "Importando..." : "Importar Questões" }}
            </button>
          </div>
        </form>
      </div>

      <div
        v-if="showAddQuestionForm"
        class="question-form mt-4 p-4 bg-gray-50 rounded-lg"
      >
        <h4 class="font-medium text-gray-800 mb-4">
          {{ editingQuestion ? "Editar questão" : "Nova questão" }}
        </h4>

        <div class="form-group mb-4">
          <label
            for="questionText"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Texto da questão (suporta Markdown)</label
          >
          <SimpleMarkdownEditor
            v-model="form.text"
            :rows="5"
            placeholder="Digite o texto da questão usando Markdown..."
          />
        </div>

        <div class="form-group mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1"
            >Alternativas</label
          >
          <div
            v-for="(alternative, index) in form.alternatives"
            :key="index"
            class="alternative-input flex items-center mb-2"
          >
            <div class="flex-1">
              <div class="flex items-center">
                <input
                  type="radio"
                  :id="`correct-${index}`"
                  :name="'correct-alternative'"
                  :value="index"
                  v-model="form.correct_alternative"
                  class="mr-2"
                />
                <input
                  :id="`alternative-${index}`"
                  v-model="alternative.text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  placeholder="Digite o texto da alternativa (suporta Markdown)"
                />
              </div>
            </div>
            <button
              v-if="form.alternatives.length > 2"
              @click="removeAlternative(index)"
              class="ml-2 text-red-500 hover:text-red-700"
              title="Remover alternativa"
            >
              <i class="ri-delete-bin-line"></i>
            </button>
          </div>

          <button
            v-if="form.alternatives.length < 6"
            @click="addAlternative"
            class="mt-2 text-sm text-indigo-600 hover:text-indigo-800"
          >
            <i class="ri-add-line mr-1"></i> Adicionar alternativa
          </button>
        </div>

        <div class="form-group mb-4">
          <label
            for="explanation"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Explicação (opcional, suporta Markdown)</label
          >
          <SimpleMarkdownEditor
            v-model="form.explanation"
            :rows="3"
            placeholder="Explique a resposta correta usando Markdown (será mostrado após o aluno responder)"
          />
        </div>

        <div class="form-actions flex justify-end space-x-3">
          <button
            @click="showAddQuestionForm = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 transition-colors"
          >
            Cancelar
          </button>
          <button
            @click="saveQuestion"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
            :disabled="isSubmitting"
          >
            {{
              isSubmitting
                ? "Salvando..."
                : editingQuestion
                ? "Atualizar"
                : "Salvar"
            }}
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :show="showDeleteModal"
      title="Excluir Questão"
      message="Tem certeza que deseja excluir esta questão? Esta ação não pode ser desfeita."
      confirm-button-text="Excluir"
      @confirm="deleteQuestion"
      @close="showDeleteModal = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { router } from "@inertiajs/vue3";
import axios from "axios";
import MarkdownRenderer from "@/Components/MarkdownRenderer.vue";
import SimpleMarkdownEditor from "@/Components/SimpleMarkdownEditor.vue";
import ConfirmationModal from "@/Components/ConfirmationModal.vue";

const props = defineProps({
  questions: {
    type: Array,
    default: () => [],
  },
  lessonId: {
    type: Number,
    required: true,
  },
  canEdit: {
    type: Boolean,
    default: false,
  },
});

const showAddQuestionForm = ref(false);
const showImportForm = ref(false);
const editingQuestion = ref(null);
const isSubmitting = ref(false);
const isImporting = ref(false);
const selectedAnswers = reactive({});
const showResults = reactive({});
const fileInput = ref(null);
const showDeleteModal = ref(false);
const questionToDelete = ref(null);

const form = reactive({
  text: "",
  explanation: "",
  alternatives: [{ text: "" }, { text: "" }],
  correct_alternative: 0,
});

const resetForm = () => {
  form.text = "";
  form.explanation = "";
  form.alternatives = [{ text: "" }, { text: "" }];
  form.correct_alternative = 0;
  editingQuestion.value = null;
};

const addAlternative = () => {
  if (form.alternatives.length < 6) {
    form.alternatives.push({ text: "" });
  }
};

const removeAlternative = (index) => {
  if (form.alternatives.length > 2) {
    form.alternatives.splice(index, 1);

    // Adjust correct_alternative if needed
    if (form.correct_alternative === index) {
      form.correct_alternative = 0;
    } else if (form.correct_alternative > index) {
      form.correct_alternative--;
    }
  }
};

const editQuestion = (question) => {
  editingQuestion.value = question;
  form.text = question.text;
  form.explanation = question.explanation || "";

  // Reset alternatives
  form.alternatives = [];

  // Add existing alternatives
  question.alternatives.forEach((alt, index) => {
    form.alternatives.push({ text: alt.text, id: alt.id });
    if (alt.is_correct) {
      form.correct_alternative = index;
    }
  });

  showAddQuestionForm.value = true;
};

const confirmDeleteQuestion = (question) => {
  questionToDelete.value = question;
  showDeleteModal.value = true;
};

const deleteQuestion = async () => {
  if (!questionToDelete.value) return;

  try {
    const response = await axios.delete(
      route("questions.destroy", questionToDelete.value.id)
    );

    if (response.data && response.data.success) {
      // Remover a questão localmente em vez de recarregar a página inteira
      const index = props.questions.findIndex(
        (q) => q.id === questionToDelete.value.id
      );
      if (index !== -1) {
        props.questions.splice(index, 1);
      }

      showDeleteModal.value = false;
      questionToDelete.value = null;
    } else {
      alert("Erro ao excluir questão. Por favor, tente novamente.");
    }
  } catch (error) {
    console.error("Erro ao excluir questão:", error);
    alert("Erro ao excluir questão. Por favor, tente novamente.");
  }
};

const saveQuestion = async () => {
  // Validate form
  if (!form.text.trim()) {
    alert("Por favor, digite o texto da questão.");
    return;
  }

  // Check if all alternatives have text
  const emptyAlternatives = form.alternatives.some((alt) => !alt.text.trim());
  if (emptyAlternatives) {
    alert("Por favor, preencha todas as alternativas.");
    return;
  }

  isSubmitting.value = true;

  try {
    if (editingQuestion.value) {
      const response = await axios.put(
        route("questions.update", editingQuestion.value.id),
        form
      );

      // Atualizar a questão localmente
      const index = props.questions.findIndex(
        (q) => q.id === editingQuestion.value.id
      );
      if (index !== -1) {
        // Criar uma cópia atualizada da questão
        const updatedQuestion = { ...props.questions[index] };
        updatedQuestion.text = form.text;
        updatedQuestion.explanation = form.explanation;

        // Atualizar alternativas
        updatedQuestion.alternatives = form.alternatives.map((alt, idx) => ({
          id: alt.id || `temp-${idx}`,
          text: alt.text,
          is_correct: idx === form.correct_alternative,
          question_id: editingQuestion.value.id,
        }));

        // Atualizar a questão no array
        props.questions.splice(index, 1, updatedQuestion);
      }

      //alert("Questão atualizada com sucesso!");
    } else {
      // Create new question
      const response = await axios.post(
        route("questions.store", props.lessonId),
        form
      );

      if (response.data && response.data.question) {
        // Adicionar a nova questão ao array local
        props.questions.push(response.data.question);
        //alert("Questão adicionada com sucesso!");
      } else {
        // Recarregar a página para obter a nova questão
        router.reload();
      }
    }

    resetForm();
    showAddQuestionForm.value = false;
  } catch (error) {
    console.error("Erro ao salvar questão:", error);

    // Exibir mensagem de erro mais detalhada
    if (error.response && error.response.data && error.response.data.errors) {
      const errorMessages = Object.values(error.response.data.errors).flat();
      alert(`Erro ao salvar questão: ${errorMessages.join("\n")}`);
    } else {
      alert("Ocorreu um erro ao salvar a questão. Por favor, tente novamente.");
    }
  } finally {
    isSubmitting.value = false;
  }
};

const selectAnswer = (questionId, alternativeId) => {
  if (!showResults[questionId]) {
    selectedAnswers[questionId] = alternativeId;
  }
};

const checkAnswer = async (question) => {
  try {
    await axios.post(route("questions.check-answer", question.id), {
      selected_alternative_id: selectedAnswers[question.id],
    });

    showResults[question.id] = true;
  } catch (error) {
    console.error("Erro ao verificar resposta:", error);
  }
};

const resetQuestion = (questionId) => {
  selectedAnswers[questionId] = null;
  showResults[questionId] = false;
};

const importQuestions = async () => {
  if (!fileInput.value || !fileInput.value.files || !fileInput.value.files[0]) {
    alert("Por favor, selecione um arquivo Excel para importar.");
    return;
  }

  const formData = new FormData();
  formData.append("file", fileInput.value.files[0]);

  isImporting.value = true;

  try {
    await axios.post(route("questions.import", props.lessonId), formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    showImportForm.value = false;
    router.reload();
  } catch (error) {
    console.error("Erro ao importar questões:", error);
    alert(
      "Ocorreu um erro ao importar as questões. Por favor, verifique o formato do arquivo e tente novamente."
    );
  } finally {
    isImporting.value = false;
  }
};
</script>

<style scoped>
.question-card {
  @apply bg-white rounded-lg shadow-sm p-4 border border-gray-200;
}

.alternative-item {
  @apply flex justify-between items-center p-3 border border-gray-200 rounded-md cursor-pointer transition-colors;
}

.alternative-item:hover:not(.selected):not(.correct):not(.incorrect) {
  @apply bg-gray-50;
}

.alternative-item.selected {
  @apply bg-indigo-50 border-indigo-300;
}

.alternative-item.correct {
  @apply bg-green-50 border-green-300;
}

.alternative-item.incorrect {
  @apply bg-red-50 border-red-300;
}

.alternative-marker {
  @apply w-5 h-5 rounded-full border border-gray-300 flex items-center justify-center mr-3;
}

.alternative-item.selected .alternative-marker {
  @apply border-indigo-500;
}

.alternative-item.correct .alternative-marker {
  @apply border-green-500;
}

.alternative-item.incorrect .alternative-marker {
  @apply border-red-500;
}

.marker-inner {
  @apply w-3 h-3 rounded-full;
}

.alternative-item.selected .marker-inner {
  @apply bg-indigo-500;
}

.alternative-item.correct .marker-inner {
  @apply bg-green-500;
}

.alternative-item.incorrect .marker-inner {
  @apply bg-red-500;
}

.alternative-text {
  @apply text-gray-700;
}

.result-icon {
  @apply text-lg;
}
</style>
