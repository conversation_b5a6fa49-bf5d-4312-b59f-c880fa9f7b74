<template>
  <div class="audio-recorder">
    <div class="recorder-controls">
      <button
        v-if="!isRecording && !audioURL"
        @click="startRecording"
        class="px-4 py-2 bg-red-600 text-white rounded-md"
      >
        <span class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <circle cx="10" cy="10" r="8" />
          </svg>
          Iniciar Gravação
        </span>
      </button>

      <button
        v-if="isRecording"
        @click="stopRecording"
        class="px-4 py-2 bg-gray-600 text-white rounded-md"
      >
        <span class="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <rect x="6" y="6" width="8" height="8" />
          </svg>
          Parar Gravação
        </span>
      </button>
    </div>

    <div v-if="audioURL" class="audio-player mt-4">
      <audio controls :src="audioURL" class="w-full"></audio>

      <div class="flex mt-2">
        <button
          @click="discardRecording"
          class="px-4 py-2 bg-gray-600 text-white rounded-md mr-2"
        >
          Descartar
        </button>

        <button
          @click="saveRecording"
          class="px-4 py-2 bg-green-600 text-white rounded-md"
        >
          Salvar Gravação
        </button>
      </div>
    </div>

    <div v-if="isRecording" class="recording-indicator mt-4">
      <div class="flex items-center">
        <div class="recording-pulse mr-2"></div>
        <span>Gravando... {{ formatTime(recordingTime) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from "vue";

const emit = defineEmits(["save"]);

const isRecording = ref(false);
const audioURL = ref(null);
const mediaRecorder = ref(null);
const audioChunks = ref([]);
const recordingTime = ref(0);
let recordingInterval = null;

const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    mediaRecorder.value = new MediaRecorder(stream);

    mediaRecorder.value.ondataavailable = (event) => {
      audioChunks.value.push(event.data);
    };

    mediaRecorder.value.onstop = () => {
      const audioBlob = new Blob(audioChunks.value, { type: "audio/mpeg" });
      audioURL.value = URL.createObjectURL(audioBlob);
      isRecording.value = false;
      clearInterval(recordingInterval);
    };

    audioChunks.value = [];
    mediaRecorder.value.start();
    isRecording.value = true;
    recordingTime.value = 0;

    recordingInterval = setInterval(() => {
      recordingTime.value += 1;
    }, 1000);
  } catch (error) {
    console.error("Error accessing microphone:", error);
    alert(
      "Não foi possível acessar o microfone. Verifique as permissões do navegador."
    );
  }
};

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop();
    mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
  }
};

const discardRecording = () => {
  audioURL.value = null;
  audioChunks.value = [];
};

const saveRecording = async () => {
  if (audioURL.value) {
    try {
      // Convert Blob URL to File object
      const response = await fetch(audioURL.value);
      const blob = await response.blob();

      // Criar um nome de arquivo único com timestamp
      const fileName = `recording-${Date.now()}.mp3`;

      // Criar um objeto File a partir do Blob
      const file = new File([blob], fileName, {
        type: "audio/mpeg",
        lastModified: new Date().getTime(),
      });

      // Emitir o evento com o arquivo e a duração
      emit("save", {
        file: file,
        duration: recordingTime.value,
      });
    } catch (error) {
      console.error("Erro ao processar o arquivo de áudio:", error);
      alert(
        "Ocorreu um erro ao processar o arquivo de áudio. Por favor, tente novamente."
      );
    }
  }
};

const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

onUnmounted(() => {
  if (mediaRecorder.value && mediaRecorder.value.state === "recording") {
    mediaRecorder.value.stop();
    mediaRecorder.value.stream.getTracks().forEach((track) => track.stop());
  }

  if (recordingInterval) {
    clearInterval(recordingInterval);
  }

  if (audioURL.value) {
    URL.revokeObjectURL(audioURL.value);
  }
});
</script>

<style scoped>
.recording-pulse {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ef4444;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}
</style>
