<template>
  <footer class="border-t border-gray-200 py-12 dark:border-gray-800">
    <div class="mx-auto max-w-7xl px-6">
      <!-- Footer main content -->
      <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
        <!-- Brand section -->
        <div class="col-span-1 lg:col-span-1">
          <div class="flex items-center gap-2">
            <ApplicationLogo class="h-10 w-auto text-indigo-600" />
            <span class="text-xl font-bold text-indigo-600">Aprovado</span>
          </div>
          <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
            Transforme sua forma de estudar com mapas mentais, gravações de
            áudio e revisão espaçada.
          </p>
        </div>

        <!-- Features section -->
        <div>
          <h3
            class="text-sm font-semibold uppercase tracking-wider text-gray-900 dark:text-gray-100"
          >
            Recursos
          </h3>
          <ul class="mt-4 space-y-3">
            <li>
              <Link
                v-if="$page.props.auth && $page.props.auth.user"
                :href="route('disciplines.index')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Disciplinas
              </Link>
              <a
                v-else
                href="#features"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Mapas Mentais
              </a>
            </li>
            <li>
              <Link
                v-if="$page.props.auth && $page.props.auth.user"
                :href="route('lessons.index')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Aulas
              </Link>
              <a
                v-else
                href="#features"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Gravações de Áudio
              </a>
            </li>
            <li>
              <Link
                v-if="$page.props.auth && $page.props.auth.user"
                :href="route('revisions.index')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Revisões
              </Link>
              <a
                v-else
                href="#features"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Revisão Espaçada
              </a>
            </li>
          </ul>
        </div>

        <!-- For who section -->
        <div>
          <h3
            class="text-sm font-semibold uppercase tracking-wider text-gray-900 dark:text-gray-100"
          >
            Para quem
          </h3>
          <ul class="mt-4 space-y-3">
            <li>
              <Link
                :href="route('university-students')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Estudantes universitários
              </Link>
            </li>
            <li>
              <Link
                :href="route('exam-students')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Concurseiros
              </Link>
            </li>
            <li>
              <Link
                :href="route('professionals')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Profissionais
              </Link>
            </li>
          </ul>
        </div>

        <!-- Contact section -->
        <div>
          <h3
            class="text-sm font-semibold uppercase tracking-wider text-gray-900 dark:text-gray-100"
          >
            Contato
          </h3>
          <ul class="mt-4 space-y-3">
            <li>
              <a
                href="mailto:<EMAIL>"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                <EMAIL>
              </a>
            </li>
            <li>
              <Link
                :href="route('privacy.policy')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Política de Privacidade
              </Link>
            </li>
            <li>
              <Link
                :href="route('terms.service')"
                class="text-sm text-gray-600 transition hover:text-indigo-600 dark:text-gray-400 dark:hover:text-indigo-400"
              >
                Termos de Uso
              </Link>
            </li>
          </ul>
        </div>
      </div>

      <!-- Footer bottom section -->
      <div class="mt-12 border-t border-gray-200 pt-8 dark:border-gray-800">
        <div
          class="flex flex-col items-center justify-between gap-6 md:flex-row"
        >
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <p>
              &copy; {{ new Date().getFullYear() }} Aprovado. Todos os direitos
              reservados.
            </p>
            <p class="mt-1">Versão {{ appVersion }}</p>
          </div>

          <!-- Social media links -->
          <div class="flex space-x-6">
            <a href="#" class="text-gray-400 transition hover:text-indigo-600">
              <span class="sr-only">Facebook</span>
              <i class="ri-facebook-fill text-2xl"></i>
            </a>
            <a href="#" class="text-gray-400 transition hover:text-indigo-600">
              <span class="sr-only">Instagram</span>
              <i class="ri-instagram-fill text-2xl"></i>
            </a>
            <a href="#" class="text-gray-400 transition hover:text-indigo-600">
              <span class="sr-only">Twitter</span>
              <i class="ri-twitter-fill text-2xl"></i>
            </a>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { Link } from "@inertiajs/vue3";
import ApplicationLogo from "@/Components/ApplicationLogo.vue";
import { computed } from "vue";

// Obter a versão do sistema do arquivo .env
const appVersion = computed(() => {
  return import.meta.env.APP_VERSION || "1.0.0";
});
</script>
