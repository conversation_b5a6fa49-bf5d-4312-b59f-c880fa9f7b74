<template>
  <div class="calendar-container">
    <div class="calendar-header">
      <div class="flex justify-between items-center mb-4">
        <button @click="prevMonth" class="px-4 py-2 bg-gray-200 rounded-md">
          &lt; Anterior
        </button>
        <h2 class="text-xl font-bold">
          {{ currentMonthName }} {{ currentYear }}
        </h2>
        <button @click="nextMonth" class="px-4 py-2 bg-gray-200 rounded-md">
          Próximo &gt;
        </button>
      </div>

      <div class="grid grid-cols-7 gap-1 mb-2">
        <div
          v-for="day in weekDays"
          :key="day"
          class="text-center font-semibold"
        >
          {{ day }}
        </div>
      </div>
    </div>

    <div class="calendar-grid grid grid-cols-7 gap-1">
      <div
        v-for="(day, index) in calendarDays"
        :key="index"
        class="calendar-day p-1 min-h-[100px] border rounded-md"
        :class="{
          'bg-gray-100': !day.isCurrentMonth,
          'bg-blue-50': day.isToday,
        }"
      >
        <div class="day-header flex justify-between items-center">
          <span
            class="day-number font-semibold"
            :class="{ 'text-blue-600': day.isToday }"
          >
            {{ day.day }}
          </span>
        </div>

        <div v-if="day.events" class="day-events mt-1">
          <div v-if="day.events.lessons && day.events.lessons.length > 0">
            <div
              v-for="lesson in day.events.lessons"
              :key="'lesson-' + lesson.id"
              class="event lesson-event p-1 mb-1 text-xs rounded bg-green-100 border-l-4 border-green-500 truncate"
              @click="goToLesson(lesson.id)"
            >
              {{ lesson.title }}
            </div>
          </div>

          <div v-if="day.events.revisions && day.events.revisions.length > 0">
            <div
              v-for="revision in day.events.revisions"
              :key="'revision-' + revision.id"
              class="event revision-event p-1 mb-1 text-xs rounded truncate"
              :class="getRevisionClass(revision, day.date)"
              @click="goToRevision(revision.id)"
            >
              {{ getRevisionLabel(revision) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { router } from "@inertiajs/vue3";

const props = defineProps({
  calendarData: {
    type: Object,
    required: true,
  },
  month: {
    type: Number,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
});

const currentMonth = ref(props.month);
const currentYear = ref(props.year);

const weekDays = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

const monthNames = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

const currentMonthName = computed(() => {
  return monthNames[currentMonth.value - 1];
});

const calendarDays = computed(() => {
  const days = [];
  const today = new Date();
  const firstDay = new Date(currentYear.value, currentMonth.value - 1, 1);
  const lastDay = new Date(currentYear.value, currentMonth.value, 0);

  // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
  const firstDayOfWeek = firstDay.getDay();

  // Add days from previous month to fill the first week
  const prevMonthLastDay = new Date(
    currentYear.value,
    currentMonth.value - 1,
    0
  ).getDate();
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i;
    const date = new Date(currentYear.value, currentMonth.value - 2, day);
    const dateStr = formatDate(date);

    days.push({
      day,
      isCurrentMonth: false,
      isToday: false,
      date: dateStr,
      events: props.calendarData[dateStr] || null,
    });
  }

  // Add days for current month
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(currentYear.value, currentMonth.value - 1, day);
    const dateStr = formatDate(date);
    const isToday =
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    days.push({
      day,
      isCurrentMonth: true,
      isToday,
      date: dateStr,
      events: props.calendarData[dateStr] || null,
    });
  }

  // Add days from next month to complete the grid (6 rows x 7 columns = 42 cells)
  const remainingDays = 42 - days.length;
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(currentYear.value, currentMonth.value, day);
    const dateStr = formatDate(date);

    days.push({
      day,
      isCurrentMonth: false,
      isToday: false,
      date: dateStr,
      events: props.calendarData[dateStr] || null,
    });
  }

  return days;
});

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const prevMonth = () => {
  if (currentMonth.value === 1) {
    currentMonth.value = 12;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }

  router.get(
    "/calendar",
    {
      month: currentMonth.value,
      year: currentYear.value,
    },
    {
      preserveState: true,
      replace: true,
    }
  );
};

const nextMonth = () => {
  if (currentMonth.value === 12) {
    currentMonth.value = 1;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }

  router.get(
    "/calendar",
    {
      month: currentMonth.value,
      year: currentYear.value,
    },
    {
      preserveState: true,
      replace: true,
    }
  );
};

const goToLesson = (lessonId) => {
  router.visit(`/lessons/${lessonId}`);
};

const goToRevision = (revisionId) => {
  router.visit(`/revisions/${revisionId}`);
};

const getRevisionLabel = (revision) => {
  const typeLabels = {
    first: "1ª Revisão",
    second: "2ª Revisão",
    third: "3ª Revisão",
    fourth: "4ª Revisão",
    recurring: "Rev. Recorrente",
    weekly: "Rev. Semanal",
  };

  return `${typeLabels[revision.type] || "Revisão"}: ${revision.lesson_title}`;
};

const isToday = (dateString) => {
  if (!dateString) return false;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Garantir que a data está no formato correto
  let revisionDate;
  if (typeof dateString === "string") {
    // Se for uma string ISO ou formato YYYY-MM-DD
    revisionDate = new Date(dateString);
  } else if (dateString instanceof Object && dateString.date) {
    // Se for um objeto Carbon do Laravel
    revisionDate = new Date(dateString.date);
  } else {
    // Caso contrário, tentar converter diretamente
    revisionDate = new Date(dateString);
  }

  revisionDate.setHours(0, 0, 0, 0);

  return revisionDate.getTime() === today.getTime();
};

const isFutureDate = (dateString) => {
  if (!dateString) return false;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Garantir que a data está no formato correto
  let revisionDate;
  if (typeof dateString === "string") {
    // Se for uma string ISO ou formato YYYY-MM-DD
    revisionDate = new Date(dateString);
  } else if (dateString instanceof Object && dateString.date) {
    // Se for um objeto Carbon do Laravel
    revisionDate = new Date(dateString.date);
  } else {
    // Caso contrário, tentar converter diretamente
    revisionDate = new Date(dateString);
  }

  revisionDate.setHours(0, 0, 0, 0);

  return revisionDate.getTime() > today.getTime();
};

const getRevisionClass = (revision, dateStr) => {
  // Se a revisão foi concluída, retorna verde
  if (revision.is_completed) {
    return "bg-green-100 border-l-4 border-green-500";
  }

  // Verifica se a data é hoje
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const revisionDate = new Date(dateStr);
  revisionDate.setHours(0, 0, 0, 0);

  // Se a data é hoje, retorna amarelo
  if (revisionDate.getTime() === today.getTime()) {
    return "bg-yellow-100 border-l-4 border-yellow-400";
  }

  // Se a data é no futuro, retorna cinza
  if (revisionDate.getTime() > today.getTime()) {
    return "bg-gray-100 border-l-4 border-gray-400";
  }

  // Se a data é no passado e não foi concluída, retorna vermelho
  return "bg-red-100 border-l-4 border-red-500";
};

watch(
  () => props.month,
  (newMonth) => {
    currentMonth.value = newMonth;
  }
);

watch(
  () => props.year,
  (newYear) => {
    currentYear.value = newYear;
  }
);
</script>

<style scoped>
.calendar-container {
  width: 100%;
}

.calendar-grid {
  min-height: 600px;
}

.calendar-day {
  transition: background-color 0.2s;
}

.calendar-day:hover {
  background-color: #f3f4f6;
}

.event {
  cursor: pointer;
  transition: transform 0.1s;
}

.event:hover {
  transform: translateX(2px);
}
</style>
