<template>
  <div class="calendar-container">
    <div class="calendar-header">
      <div class="flex justify-between items-center mb-3 sm:mb-4">
        <button
          @click="prevMonth"
          class="flex items-center justify-center px-3 py-2 sm:px-4 bg-gray-200 hover:bg-gray-300 rounded-md text-sm sm:text-base font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white"
        >
          <i class="ri-arrow-left-line sm:mr-1"></i>
          <span class="hidden sm:inline">Anterior</span>
        </button>
        <h2
          class="text-lg sm:text-xl font-bold text-gray-900 dark:text-white text-center"
        >
          {{ currentMonthName }} {{ currentYear }}
        </h2>
        <button
          @click="nextMonth"
          class="flex items-center justify-center px-3 py-2 sm:px-4 bg-gray-200 hover:bg-gray-300 rounded-md text-sm sm:text-base font-medium transition-colors dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white"
        >
          <span class="hidden sm:inline">Próximo</span>
          <i class="ri-arrow-right-line sm:ml-1"></i>
        </button>
      </div>

      <div class="grid grid-cols-7 gap-1 mb-2">
        <div
          v-for="day in weekDays"
          :key="day"
          class="text-center font-semibold text-xs sm:text-sm text-gray-700 dark:text-gray-300 py-2"
        >
          {{ day }}
        </div>
      </div>
    </div>

    <div class="calendar-grid grid grid-cols-7 gap-0.5 sm:gap-1">
      <div
        v-for="(day, index) in calendarDays"
        :key="index"
        class="calendar-day p-1 sm:p-2 min-h-[60px] sm:min-h-[100px] border rounded-sm sm:rounded-md transition-colors"
        :class="{
          'bg-gray-100 dark:bg-gray-700': !day.isCurrentMonth,
          'bg-blue-50 dark:bg-blue-900/30': day.isToday,
          'bg-white dark:bg-gray-800': day.isCurrentMonth && !day.isToday,
        }"
      >
        <div class="day-header flex justify-between items-center">
          <span
            class="day-number font-semibold text-xs sm:text-sm"
            :class="{
              'text-blue-600 dark:text-blue-400': day.isToday,
              'text-gray-900 dark:text-white':
                day.isCurrentMonth && !day.isToday,
              'text-gray-400 dark:text-gray-500': !day.isCurrentMonth,
            }"
          >
            {{ day.day }}
          </span>
        </div>

        <div
          v-if="day.events"
          class="day-events mt-0.5 sm:mt-1 space-y-0.5 sm:space-y-1"
        >
          <div v-if="day.events.lessons && day.events.lessons.length > 0">
            <div
              v-for="lesson in day.events.lessons"
              :key="'lesson-' + lesson.id"
              class="event lesson-event p-0.5 sm:p-1 text-xs rounded bg-green-100 border-l-2 sm:border-l-4 border-green-500 truncate cursor-pointer hover:bg-green-200 transition-colors"
              @click="goToLesson(lesson.id)"
              :title="lesson.title"
            >
              <span class="hidden sm:inline">{{ lesson.title }}</span>
              <span class="sm:hidden"
                >{{ lesson.title.substring(0, 8)
                }}{{ lesson.title.length > 8 ? "..." : "" }}</span
              >
            </div>
          </div>

          <div v-if="day.events.revisions && day.events.revisions.length > 0">
            <div
              v-for="revision in day.events.revisions"
              :key="'revision-' + revision.id"
              class="event revision-event p-0.5 sm:p-1 text-xs rounded truncate cursor-pointer transition-colors"
              :class="getRevisionClass(revision, day.date)"
              @click="goToRevision(revision.id)"
              :title="getRevisionLabel(revision)"
            >
              <span class="hidden sm:inline">{{
                getRevisionLabel(revision)
              }}</span>
              <span class="sm:hidden">{{
                getRevisionLabelShort(revision)
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { router } from "@inertiajs/vue3";

const props = defineProps({
  calendarData: {
    type: Object,
    required: true,
  },
  month: {
    type: Number,
    required: true,
  },
  year: {
    type: Number,
    required: true,
  },
});

const currentMonth = ref(props.month);
const currentYear = ref(props.year);

const weekDays = ["Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb"];

const monthNames = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro",
];

const currentMonthName = computed(() => {
  return monthNames[currentMonth.value - 1];
});

const calendarDays = computed(() => {
  const days = [];
  const today = new Date();
  const firstDay = new Date(currentYear.value, currentMonth.value - 1, 1);
  const lastDay = new Date(currentYear.value, currentMonth.value, 0);

  // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
  const firstDayOfWeek = firstDay.getDay();

  // Add days from previous month to fill the first week
  const prevMonthLastDay = new Date(
    currentYear.value,
    currentMonth.value - 1,
    0
  ).getDate();
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i;
    const date = new Date(currentYear.value, currentMonth.value - 2, day);
    const dateStr = formatDate(date);

    days.push({
      day,
      isCurrentMonth: false,
      isToday: false,
      date: dateStr,
      events: props.calendarData[dateStr] || null,
    });
  }

  // Add days for current month
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const date = new Date(currentYear.value, currentMonth.value - 1, day);
    const dateStr = formatDate(date);
    const isToday =
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();

    days.push({
      day,
      isCurrentMonth: true,
      isToday,
      date: dateStr,
      events: props.calendarData[dateStr] || null,
    });
  }

  // Add days from next month to complete the grid (6 rows x 7 columns = 42 cells)
  const remainingDays = 42 - days.length;
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(currentYear.value, currentMonth.value, day);
    const dateStr = formatDate(date);

    days.push({
      day,
      isCurrentMonth: false,
      isToday: false,
      date: dateStr,
      events: props.calendarData[dateStr] || null,
    });
  }

  return days;
});

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const prevMonth = () => {
  if (currentMonth.value === 1) {
    currentMonth.value = 12;
    currentYear.value--;
  } else {
    currentMonth.value--;
  }

  router.get(
    "/calendar",
    {
      month: currentMonth.value,
      year: currentYear.value,
    },
    {
      preserveState: true,
      replace: true,
    }
  );
};

const nextMonth = () => {
  if (currentMonth.value === 12) {
    currentMonth.value = 1;
    currentYear.value++;
  } else {
    currentMonth.value++;
  }

  router.get(
    "/calendar",
    {
      month: currentMonth.value,
      year: currentYear.value,
    },
    {
      preserveState: true,
      replace: true,
    }
  );
};

const goToLesson = (lessonId) => {
  router.visit(`/lessons/${lessonId}`);
};

const goToRevision = (revisionId) => {
  router.visit(`/revisions/${revisionId}`);
};

const getRevisionLabel = (revision) => {
  const typeLabels = {
    first: "1ª Revisão",
    second: "2ª Revisão",
    third: "3ª Revisão",
    fourth: "4ª Revisão",
    recurring: "Rev. Recorrente",
    weekly: "Rev. Semanal",
  };

  return `${typeLabels[revision.type] || "Revisão"}: ${revision.lesson_title}`;
};

const getRevisionLabelShort = (revision) => {
  const typeLabels = {
    first: "1ª",
    second: "2ª",
    third: "3ª",
    fourth: "4ª",
    recurring: "Rec",
    weekly: "Sem",
  };

  return typeLabels[revision.type] || "Rev";
};

const isToday = (dateString) => {
  if (!dateString) return false;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Garantir que a data está no formato correto
  let revisionDate;
  if (typeof dateString === "string") {
    // Se for uma string ISO ou formato YYYY-MM-DD
    revisionDate = new Date(dateString);
  } else if (dateString instanceof Object && dateString.date) {
    // Se for um objeto Carbon do Laravel
    revisionDate = new Date(dateString.date);
  } else {
    // Caso contrário, tentar converter diretamente
    revisionDate = new Date(dateString);
  }

  revisionDate.setHours(0, 0, 0, 0);

  return revisionDate.getTime() === today.getTime();
};

const isFutureDate = (dateString) => {
  if (!dateString) return false;

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Garantir que a data está no formato correto
  let revisionDate;
  if (typeof dateString === "string") {
    // Se for uma string ISO ou formato YYYY-MM-DD
    revisionDate = new Date(dateString);
  } else if (dateString instanceof Object && dateString.date) {
    // Se for um objeto Carbon do Laravel
    revisionDate = new Date(dateString.date);
  } else {
    // Caso contrário, tentar converter diretamente
    revisionDate = new Date(dateString);
  }

  revisionDate.setHours(0, 0, 0, 0);

  return revisionDate.getTime() > today.getTime();
};

const getRevisionClass = (revision, dateStr) => {
  // Se a revisão foi concluída, retorna verde
  if (revision.is_completed) {
    return "bg-green-100 border-l-2 sm:border-l-4 border-green-500 hover:bg-green-200";
  }

  // Verifica se a data é hoje
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const revisionDate = new Date(dateStr);
  revisionDate.setHours(0, 0, 0, 0);

  // Se a data é hoje, retorna amarelo
  if (revisionDate.getTime() === today.getTime()) {
    return "bg-yellow-100 border-l-2 sm:border-l-4 border-yellow-400 hover:bg-yellow-200";
  }

  // Se a data é no futuro, retorna cinza
  if (revisionDate.getTime() > today.getTime()) {
    return "bg-gray-100 border-l-2 sm:border-l-4 border-gray-400 hover:bg-gray-200";
  }

  // Se a data é no passado e não foi concluída, retorna vermelho
  return "bg-red-100 border-l-2 sm:border-l-4 border-red-500 hover:bg-red-200";
};

watch(
  () => props.month,
  (newMonth) => {
    currentMonth.value = newMonth;
  }
);

watch(
  () => props.year,
  (newYear) => {
    currentYear.value = newYear;
  }
);
</script>

<style scoped>
.calendar-container {
  width: 100%;
}

.calendar-grid {
  min-height: 400px;
}

@media (min-width: 640px) {
  .calendar-grid {
    min-height: 600px;
  }
}

.calendar-day {
  transition: background-color 0.2s, border-color 0.2s;
  border-color: #e5e7eb;
}

.calendar-day:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.dark .calendar-day {
  border-color: #374151;
}

.dark .calendar-day:hover {
  background-color: #1f2937;
  border-color: #4b5563;
}

.event {
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.event:hover {
  transform: translateX(1px);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

@media (min-width: 640px) {
  .event:hover {
    transform: translateX(2px);
  }
}

/* Melhor visualização em telas pequenas */
@media (max-width: 639px) {
  .calendar-day {
    font-size: 0.75rem;
  }

  .day-events {
    max-height: 40px;
    overflow: hidden;
  }

  .event {
    font-size: 0.625rem;
    line-height: 1.2;
  }
}
</style>
