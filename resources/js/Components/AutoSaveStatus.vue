<template>
  <div
    v-if="showStatus"
    class="flex items-center space-x-2 text-sm transition-all duration-300"
    :class="statusClasses"
  >
    <div class="flex items-center space-x-1">
      <!-- Loading spinner -->
      <i
        v-if="status === 'saving'"
        class="ri-loader-4-line animate-spin"
      ></i>
      
      <!-- Success icon -->
      <i
        v-else-if="status === 'saved'"
        class="ri-check-line"
      ></i>
      
      <!-- Error icon -->
      <i
        v-else-if="status === 'error'"
        class="ri-error-warning-line"
      ></i>
      
      <!-- Idle icon (optional) -->
      <i
        v-else-if="showIdleIcon"
        class="ri-save-line"
      ></i>
    </div>
    
    <span v-if="message">{{ message }}</span>
    
    <!-- Last saved time -->
    <span
      v-if="lastSaved && status === 'saved'"
      class="text-xs opacity-75"
    >
      {{ formatLastSaved }}
    </span>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  status: {
    type: String,
    default: 'idle',
    validator: (value) => ['idle', 'saving', 'saved', 'error'].includes(value),
  },
  message: {
    type: String,
    default: '',
  },
  lastSaved: {
    type: Date,
    default: null,
  },
  showIdleIcon: {
    type: Boolean,
    default: false,
  },
  compact: {
    type: Boolean,
    default: false,
  },
});

const showStatus = computed(() => {
  return props.status !== 'idle' || props.showIdleIcon;
});

const statusClasses = computed(() => {
  const baseClasses = props.compact ? 'px-2 py-1' : 'px-3 py-2';
  
  switch (props.status) {
    case 'saving':
      return `${baseClasses} text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md`;
    case 'saved':
      return `${baseClasses} text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md`;
    case 'error':
      return `${baseClasses} text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md`;
    default:
      return `${baseClasses} text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md`;
  }
});

const formatLastSaved = computed(() => {
  if (!props.lastSaved) return '';
  
  const now = new Date();
  const diff = now - props.lastSaved;
  
  if (diff < 60000) { // Menos de 1 minuto
    return 'agora mesmo';
  } else if (diff < 3600000) { // Menos de 1 hora
    const minutes = Math.floor(diff / 60000);
    return `há ${minutes} min`;
  } else {
    return props.lastSaved.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
    });
  }
});
</script>
