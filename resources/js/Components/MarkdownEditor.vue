<template>
  <div class="markdown-editor">
    <div ref="editorContainer" class="editor-container"></div>
    <div v-if="showHelp" class="markdown-help mt-4">
      <div class="help-header">
        <h3 class="text-lg font-medium text-gray-900">Guia de Sintaxe Markdown</h3>
        <p class="text-sm text-gray-600 mt-1">
          Use a sintaxe abaixo para criar seu mapa mental:
        </p>
      </div>
      <div class="help-content mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="syntax-guide bg-gray-50 p-4 rounded-md">
          <h4 class="text-md font-medium text-gray-800 mb-2">Sintaxe Básica</h4>
          <ul class="space-y-2 text-sm">
            <li><code class="bg-gray-200 px-1 py-0.5 rounded"># Título</code> - Tí<PERSON>lo principal (nível 1)</li>
            <li><code class="bg-gray-200 px-1 py-0.5 rounded">## Subtítulo</code> - Subtítulo (nível 2)</li>
            <li><code class="bg-gray-200 px-1 py-0.5 rounded">### Tópico</code> - Tópico (nível 3)</li>
            <li><code class="bg-gray-200 px-1 py-0.5 rounded">- Item</code> - Item de lista</li>
            <li><code class="bg-gray-200 px-1 py-0.5 rounded">  - Subitem</code> - Subitem (com indentação)</li>
            <li><code class="bg-gray-200 px-1 py-0.5 rounded">**Texto**</code> - Texto em negrito</li>
            <li><code class="bg-gray-200 px-1 py-0.5 rounded">*Texto*</code> - Texto em itálico</li>
          </ul>
        </div>
        <div class="example-code bg-gray-50 p-4 rounded-md">
          <h4 class="text-md font-medium text-gray-800 mb-2">Exemplo</h4>
          <pre class="text-sm bg-white p-3 rounded border border-gray-200 overflow-auto">
# Meu Mapa Mental
## Conceitos Principais
- Ideia 1
  - Subideia 1.1
  - Subideia 1.2
## Aplicações
- Exemplo 1
- Exemplo 2
  - Detalhes
    - **Importante**
    - *Observação*</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { EditorView, basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { markdown } from '@codemirror/lang-markdown';
import { oneDark } from '@codemirror/theme-one-dark';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  darkMode: {
    type: Boolean,
    default: false
  },
  showHelp: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue']);

const editorContainer = ref(null);
let editorView = null;

// Função para criar o editor
const createEditor = () => {
  if (!editorContainer.value) return;
  
  // Configuração do tema baseado na prop darkMode
  const theme = props.darkMode ? oneDark : [];
  
  // Criar o estado do editor
  const state = EditorState.create({
    doc: props.modelValue,
    extensions: [
      basicSetup,
      markdown(),
      theme,
      EditorView.updateListener.of(update => {
        if (update.docChanged) {
          emit('update:modelValue', update.state.doc.toString());
        }
      }),
      EditorView.theme({
        '&': {
          height: '400px',
          border: '1px solid #e5e7eb',
          borderRadius: '0.375rem',
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5'
        },
        '.cm-scroller': {
          overflow: 'auto',
          fontFamily: 'monospace'
        },
        '.cm-content': {
          padding: '10px'
        },
        '.cm-line': {
          padding: '0 4px'
        },
        // Estilização para sintaxe Markdown
        '.cm-header': { color: '#4F46E5', fontWeight: 'bold' },
        '.cm-header-1': { fontSize: '1.2em' },
        '.cm-header-2': { fontSize: '1.1em' },
        '.cm-header-3': { fontSize: '1.05em' },
        '.cm-strong': { fontWeight: 'bold', color: '#1F2937' },
        '.cm-em': { fontStyle: 'italic', color: '#4B5563' },
        '.cm-list': { color: '#4F46E5' },
        '.cm-link': { color: '#2563EB' }
      })
    ]
  });
  
  // Criar a visualização do editor
  editorView = new EditorView({
    state,
    parent: editorContainer.value
  });
};

// Função para lidar com a tecla Tab
const handleTab = (event) => {
  if (event.key === 'Tab') {
    event.preventDefault();
    const doc = editorView.state.doc;
    const selection = editorView.state.selection.main;
    const line = doc.lineAt(selection.from);
    const indentation = '  '; // Dois espaços para indentação
    
    editorView.dispatch({
      changes: {
        from: line.from,
        to: line.from,
        insert: indentation
      }
    });
  }
};

// Montar o editor quando o componente for montado
onMounted(() => {
  createEditor();
  
  // Adicionar manipulador de eventos para a tecla Tab
  if (editorContainer.value) {
    editorContainer.value.addEventListener('keydown', handleTab);
  }
});

// Limpar quando o componente for desmontado
onUnmounted(() => {
  if (editorView) {
    editorView.destroy();
  }
  
  if (editorContainer.value) {
    editorContainer.value.removeEventListener('keydown', handleTab);
  }
});

// Observar mudanças na prop modelValue
watch(() => props.modelValue, (newValue) => {
  if (editorView && newValue !== editorView.state.doc.toString()) {
    editorView.dispatch({
      changes: {
        from: 0,
        to: editorView.state.doc.length,
        insert: newValue
      }
    });
  }
});

// Observar mudanças na prop darkMode
watch(() => props.darkMode, () => {
  if (editorView) {
    editorView.destroy();
    createEditor();
  }
});
</script>

<style scoped>
.markdown-editor {
  width: 100%;
}

.editor-container {
  width: 100%;
  min-height: 400px;
}

.markdown-help {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}
</style>
