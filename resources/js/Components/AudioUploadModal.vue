<template>
  <Modal :show="show" @close="closeModal">
    <div class="p-6">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        Upload de Áudio
      </h2>
      <div class="mt-4">
        <div v-if="!selectedFile">
          <div
            class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6 dark:border-gray-700"
          >
            <i
              class="ri-upload-cloud-line mb-2 text-4xl text-gray-400 dark:text-gray-500"
            ></i>
            <p class="mb-2 text-sm text-gray-600 dark:text-gray-400">
              Clique para selecionar ou arraste um arquivo de áudio
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-500">
              MP3, WAV, OGG (máx. 20MB)
            </p>
            <input
              type="file"
              class="absolute h-full w-full cursor-pointer opacity-0"
              accept="audio/mp3,audio/mpeg,audio/wav,audio/ogg"
              @change="handleFileChange"
            />
          </div>
        </div>
        <div v-else>
          <div class="mb-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <div class="flex items-center">
              <i
                class="ri-music-line mr-3 text-2xl text-indigo-500 dark:text-indigo-400"
              ></i>
              <div class="ml-2 flex-1 truncate">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ selectedFile.name }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ formatFileSize(selectedFile.size) }}
                </p>
              </div>
              <button
                @click="selectedFile = null"
                class="ml-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <i class="ri-close-line text-xl"></i>
              </button>
            </div>
          </div>

          <div class="mb-4">
            <InputLabel for="audio_title" value="Título do Áudio" />
            <TextInput
              id="audio_title"
              type="text"
              class="mt-1 block w-full"
              v-model="audioTitle"
              required
              autofocus
            />
            <InputError class="mt-2" :message="titleError" />
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeModal">Cancelar</SecondaryButton>
          <PrimaryButton
            @click="uploadAudio"
            :class="{ 'opacity-25': isUploading }"
            :disabled="isUploading || !selectedFile || !audioTitle"
          >
            <i
              v-if="isUploading"
              class="ri-loader-4-line mr-2 animate-spin"
            ></i>
            {{ isUploading ? "Enviando..." : "Enviar Áudio" }}
          </PrimaryButton>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import { ref } from "vue";
import { useForm } from "@inertiajs/vue3";
import Modal from "@/Components/Modal.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import TextInput from "@/Components/TextInput.vue";

const props = defineProps({
  show: Boolean,
  lessonId: Number,
});

const emit = defineEmits(["close", "uploaded"]);

const selectedFile = ref(null);
const audioTitle = ref("");
const titleError = ref("");
const isUploading = ref(false);

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    // Check if file is an audio file
    if (!file.type.startsWith("audio/")) {
      alert("Por favor, selecione um arquivo de áudio válido.");
      return;
    }

    // Check file size (max 20MB)
    if (file.size > 20 * 1024 * 1024) {
      alert("O arquivo é muito grande. O tamanho máximo é 20MB.");
      return;
    }

    selectedFile.value = file;
    // Set a default title based on the file name
    audioTitle.value = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const uploadAudio = () => {
  if (!selectedFile.value || !audioTitle.value) {
    if (!audioTitle.value) {
      titleError.value = "O título é obrigatório";
    }
    return;
  }

  titleError.value = "";
  isUploading.value = true;

  // Let's use Inertia's form handling instead of XMLHttpRequest
  // This is more reliable and handles CSRF automatically

  const form = useForm({
    title: audioTitle.value,
    audio_file: selectedFile.value,
    duration_seconds: null,
    lesson_id: props.lessonId,
  });

  form.post(route("audio-recordings.store"), {
    forceFormData: true,
    preserveScroll: true,
    onSuccess: () => {
      isUploading.value = false;
      closeModal();
      // Emit the uploaded event after the modal is closed
      setTimeout(() => {
        emit("uploaded");
      }, 300);
    },
    onError: (errors) => {
      console.error("Upload failed with errors:", errors);
      isUploading.value = false;
      if (errors.title) {
        titleError.value = errors.title;
      } else if (errors.audio_file) {
        alert(`Erro: ${errors.audio_file}`);
      } else {
        alert("Erro ao enviar áudio. Por favor, tente novamente.");
      }
    },
    onFinish: () => {
      isUploading.value = false;
    },
  });
};

const closeModal = () => {
  selectedFile.value = null;
  audioTitle.value = "";
  titleError.value = "";
  isUploading.value = false;
  emit("close");
};
</script>
