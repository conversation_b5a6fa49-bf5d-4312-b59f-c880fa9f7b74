<template>
  <Modal :show="show" @close="closeModal">
    <div class="p-6">
      <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
        Upload de Áudio
      </h2>
      <div class="mt-4">
        <div v-if="!selectedFile">
          <div
            class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 p-6 dark:border-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="mb-2 h-10 w-10 text-gray-400 dark:text-gray-500"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9 8.25H7.5a2.25 2.25 0 00-2.25 2.25v9a2.25 2.25 0 002.25 2.25h9a2.25 2.25 0 002.25-2.25v-9a2.25 2.25 0 00-2.25-2.25H15m0-3l-3-3m0 0l-3 3m3-3V15"
              />
            </svg>
            <p class="mb-2 text-sm text-gray-600 dark:text-gray-400">
              Clique para selecionar ou arraste um arquivo de áudio
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-500">
              MP3, WAV, OGG (máx. 20MB)
            </p>
            <input
              type="file"
              class="absolute h-full w-full cursor-pointer opacity-0"
              accept="audio/mp3,audio/mpeg,audio/wav,audio/ogg"
              @change="handleFileChange"
            />
          </div>
        </div>
        <div v-else>
          <div class="mb-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800">
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-3 h-6 w-6 text-indigo-500 dark:text-indigo-400"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9 9l10.5-3m0 6.553v3.75a2.25 2.25 0 01-1.632 2.163l-1.32.377a1.803 1.803 0 11-.99-3.467l2.31-.66a2.25 2.25 0 001.632-2.163zm0 0V2.25L9 5.25v10.303m0 0v3.75a2.25 2.25 0 01-1.632 2.163l-1.32.377a1.803 1.803 0 01-.99-3.467l2.31-.66A2.25 2.25 0 009 15.553z"
                />
              </svg>
              <div class="ml-2 flex-1 truncate">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ selectedFile.name }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  {{ formatFileSize(selectedFile.size) }}
                </p>
              </div>
              <button
                @click="selectedFile = null"
                class="ml-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-5 w-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>

          <div class="mb-4">
            <InputLabel for="audio_title" value="Título do Áudio" />
            <TextInput
              id="audio_title"
              type="text"
              class="mt-1 block w-full"
              v-model="audioTitle"
              required
              autofocus
            />
            <InputError class="mt-2" :message="titleError" />
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeModal">Cancelar</SecondaryButton>
          <PrimaryButton
            @click="uploadAudio"
            :class="{ 'opacity-25': isUploading }"
            :disabled="isUploading || !selectedFile || !audioTitle"
          >
            <svg
              v-if="isUploading"
              class="mr-2 h-4 w-4 animate-spin"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ isUploading ? "Enviando..." : "Enviar Áudio" }}
          </PrimaryButton>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import { ref } from "vue";
import { useForm } from "@inertiajs/vue3";
import Modal from "@/Components/Modal.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import TextInput from "@/Components/TextInput.vue";

const props = defineProps({
  show: Boolean,
  lessonId: Number,
});

const emit = defineEmits(["close", "uploaded"]);

const selectedFile = ref(null);
const audioTitle = ref("");
const titleError = ref("");
const isUploading = ref(false);

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    // Check if file is an audio file
    if (!file.type.startsWith("audio/")) {
      alert("Por favor, selecione um arquivo de áudio válido.");
      return;
    }

    // Check file size (max 20MB)
    if (file.size > 20 * 1024 * 1024) {
      alert("O arquivo é muito grande. O tamanho máximo é 20MB.");
      return;
    }

    selectedFile.value = file;
    // Set a default title based on the file name
    audioTitle.value = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
  }
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const uploadAudio = () => {
  if (!selectedFile.value || !audioTitle.value) {
    if (!audioTitle.value) {
      titleError.value = "O título é obrigatório";
    }
    return;
  }

  titleError.value = "";
  isUploading.value = true;

  // Let's use Inertia's form handling instead of XMLHttpRequest
  // This is more reliable and handles CSRF automatically

  const form = useForm({
    title: audioTitle.value,
    audio_file: selectedFile.value,
    duration_seconds: null,
    lesson_id: props.lessonId,
  });

  form.post(route("audio-recordings.store"), {
    forceFormData: true,
    preserveScroll: true,
    onSuccess: () => {
      isUploading.value = false;
      closeModal();
      // Emit the uploaded event after the modal is closed
      setTimeout(() => {
        emit("uploaded");
      }, 300);
    },
    onError: (errors) => {
      console.error("Upload failed with errors:", errors);
      isUploading.value = false;
      if (errors.title) {
        titleError.value = errors.title;
      } else if (errors.audio_file) {
        alert(`Erro: ${errors.audio_file}`);
      } else {
        alert("Erro ao enviar áudio. Por favor, tente novamente.");
      }
    },
    onFinish: () => {
      isUploading.value = false;
    },
  });
};

const closeModal = () => {
  selectedFile.value = null;
  audioTitle.value = "";
  titleError.value = "";
  isUploading.value = false;
  emit("close");
};
</script>
