<template>
  <div class="emoticon-panel">
    <div
      v-for="(emoji, index) in emojis"
      :key="index"
      class="emoticon-item"
      @click="copyEmoji(emoji.symbol, index)"
    >
      <span class="copied-msg" v-if="copiedIndex === index">Copiado!</span>
      <span class="emoticon-symbol">{{ emoji.symbol }}</span>
      <span class="emoticon-label">{{ emoji.label }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const emojis = [
  // ✅ Status & Ações
  { symbol: "✅", label: "Concluído" },
  { symbol: "❌", label: "Erro/Cancelado" },
  { symbol: "⏸️", label: "Pausado" },
  { symbol: "🔄", label: "Revisar" },
  { symbol: "📤", label: "Exportar" },
  { symbol: "📥", label: "Importar" },
  { symbol: "✂️", label: "Cortar" },
  { symbol: "📋", label: "Copiar" },
  { symbol: "🔒", label: "Bloquear" },
  { symbol: "🔓", label: "Desbloquear" },

  // ⚠️ Alertas & Prioridades
  { symbol: "⚠️", label: "Atenção" },
  { symbol: "🚨", label: "Urgente" },
  { symbol: "🔔", label: "Lembrete" },
  { symbol: "❗", label: "Importante" },
  { symbol: "❓", label: "Dúvida" },
  { symbol: "🔴", label: "Crítico" },
  { symbol: "🟡", label: "Alerta" },
  { symbol: "🟢", label: "OK" },
  { symbol: "⛔", label: "Proibido/Ignorar" },
  { symbol: "💢", label: "Problema" },

  // 💡 Ideias & Criatividade
  { symbol: "💡", label: "Ideia" },
  { symbol: "✨", label: "Inspiração" },
  { symbol: "🎨", label: "Criatividade" },
  { symbol: "🧩", label: "Quebra-cabeça" },
  { symbol: "🔮", label: "Previsão" },
  { symbol: "🌱", label: "Crescimento" },
  { symbol: "🚀", label: "Inovação" },
  { symbol: "🧪", label: "Experimento" },
  { symbol: "📐", label: "Planejamento" },
  { symbol: "🎲", label: "Aleatoriedade" },

  // 📌 Organização & Estrutura
  { symbol: "📌", label: "Fixar" },
  { symbol: "📍", label: "Marcador" },
  { symbol: "🗂️", label: "Categorizar" },
  { symbol: "📂", label: "Pasta" },
  { symbol: "📝", label: "Anotação" },
  { symbol: "📑", label: "Documento" },
  { symbol: "🔖", label: "Bookmark" },
  { symbol: "🏷️", label: "Etiqueta" },
  { symbol: "📊", label: "Gráfico" },
  { symbol: "📉", label: "Queda" },

  // 🧠 Estudo & Aprendizado
  { symbol: "🧠", label: "Memorização" },
  { symbol: "📖", label: "Livro" },
  { symbol: "📚", label: "Biblioteca" },
  { symbol: "🎓", label: "Formação" },
  { symbol: "📝", label: "Exercício" },
  { symbol: "✍️", label: "Escrever" },
  { symbol: "🔍", label: "Pesquisar" },
  { symbol: "🔎", label: "Detalhe" },
  { symbol: "📌", label: "Destaque" },
  { symbol: "📜", label: "Certificado" },

  // ⏳ Tempo & Produtividade
  { symbol: "⏳", label: "Pendente" },
  { symbol: "⌛", label: "Tempo esgotando" },
  { symbol: "⏰", label: "Horário" },
  { symbol: "📅", label: "Calendário" },
  { symbol: "🗓️", label: "Agenda" },
  { symbol: "🕒", label: "Espera" },
  { symbol: "🚩", label: "Marco" },
  { symbol: "📆", label: "Prazo" },
  { symbol: "⏱️", label: "Cronômetro" },
  { symbol: "🔄", label: "Recorrente" },

  // 🔗 Links & Conexões
  { symbol: "🔗", label: "Link" },
  { symbol: "📎", label: "Anexo" },
  { symbol: "🖇️", label: "Conectar" },
  { symbol: "🌐", label: "Internet" },
  { symbol: "📡", label: "Rede" },
  { symbol: "🔌", label: "Conectar" },
  { symbol: "💻", label: "Tecnologia" },
  { symbol: "📱", label: "Mobile" },
  { symbol: "📞", label: "Contato" },
  { symbol: "📨", label: "Mensagem" },

  // 🎯 Metas & Objetivos
  { symbol: "🎯", label: "Meta" },
  { symbol: "🏆", label: "Conquista" },
  { symbol: "🥇", label: "Primeiro lugar" },
  { symbol: "📌", label: "Foco" },
  { symbol: "🚩", label: "Objetivo" },
  { symbol: "⛳", label: "Finalização" },
  { symbol: "🏁", label: "Conclusão" },
  { symbol: "📈", label: "Progresso" },
  { symbol: "📉", label: "Regressão" },
  { symbol: "📊", label: "Análise" },

  // ❤️ Emoções & Feedback
  { symbol: "❤️", label: "Gostei" },
  { symbol: "👍", label: "Aprovação" },
  { symbol: "👎", label: "Reprovação" },
  { symbol: "😊", label: "Satisfação" },
  { symbol: "😢", label: "Tristeza" },
  { symbol: "😡", label: "Raiva" },
  { symbol: "😴", label: "Cansado" },
  { symbol: "🤔", label: "Reflexão" },
  { symbol: "🎉", label: "Celebração" },
  { symbol: "🙏", label: "Gratidão" },
];

const copiedIndex = ref(null);

function copyEmoji(emoji, index) {
  navigator.clipboard.writeText(emoji);
  copiedIndex.value = index;
  setTimeout(() => {
    copiedIndex.value = null;
  }, 1000);
}
</script>

<style scoped>
.emoticon-panel {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
  gap: 12px;
  padding: 10px;
  max-width: 100%;
  margin: 0 auto;
}

.emoticon-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 10px;
  border: 1px solid #ddd;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s ease;
}

.emoticon-item:hover {
  background-color: #eef;
  transform: scale(1.05);
}

.emoticon-symbol {
  font-size: 24px;
  margin-bottom: 6px;
}

.emoticon-label {
  font-size: 13px;
  color: #333;
  text-align: center;
}

.copied-msg {
  position: absolute;
  top: -20px;
  background: #4caf50;
  color: white;
  padding: 3px 8px;
  border-radius: 5px;
  font-size: 11px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.emoticon-item:hover .copied-msg,
.emoticon-item .copied-msg {
  opacity: 1;
}
</style>
