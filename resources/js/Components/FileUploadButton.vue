<template>
  <div class="file-upload-button">
    <button
      type="button"
      @click="triggerFileInput"
      class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="mr-1.5 h-4 w-4"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
        />
      </svg>
      {{ buttonText }}
    </button>
    <input
      type="file"
      ref="fileInput"
      class="hidden"
      :accept="accept"
      @change="handleFileChange"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  buttonText: {
    type: String,
    default: "Upload File",
  },
  accept: {
    type: String,
    default: "*/*",
  },
});

const emit = defineEmits(["file-selected"]);
const fileInput = ref(null);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    emit("file-selected", file);
    // Reset the input so the same file can be selected again
    event.target.value = "";
  }
};
</script>
