<template>
  <div class="file-upload-button">
    <button
      type="button"
      @click="triggerFileInput"
      class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
    >
      <i class="ri-upload-line mr-1.5"></i>
      {{ buttonText }}
    </button>
    <input
      type="file"
      ref="fileInput"
      class="hidden"
      :accept="accept"
      @change="handleFileChange"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";

const props = defineProps({
  buttonText: {
    type: String,
    default: "Upload File",
  },
  accept: {
    type: String,
    default: "*/*",
  },
});

const emit = defineEmits(["file-selected"]);
const fileInput = ref(null);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    emit("file-selected", file);
    // Reset the input so the same file can be selected again
    event.target.value = "";
  }
};
</script>
