<template>
  <Head title="Calendário" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Calendário de Revisões
        </h2>
        <div class="flex gap-3">
          <Link
            :href="route('revisions.index')"
            class="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="-ml-1 mr-2 h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
              />
            </svg>
            Ver Lista de Revisões
          </Link>
          <Link
            :href="route('lessons.create')"
            class="inline-flex items-center rounded-md border border-indigo-600 bg-white px-4 py-2 text-sm font-medium text-indigo-700 shadow-sm hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            <svg
              class="-ml-1 mr-2 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Nova Aula
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Calendar Header -->
        <div class="mb-6 flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900">
            Visualize suas revisões agendadas
          </h3>
          <div class="flex items-center space-x-4">
            <span class="text-sm font-medium text-gray-700">
              {{
                new Date(year, month - 1).toLocaleDateString("pt-BR", {
                  month: "long",
                  year: "numeric",
                })
              }}
            </span>
          </div>
        </div>

        <!-- Calendar Container -->
        <div
          class="overflow-hidden rounded-lg bg-white shadow-md border border-gray-100"
        >
          <div class="p-6">
            <Calendar
              :calendar-data="calendarData"
              :month="month"
              :year="year"
            />
          </div>
        </div>

        <!-- Legend -->
        <div
          class="mt-6 overflow-hidden rounded-lg bg-white shadow-md border border-gray-100"
        >
          <div class="p-4">
            <h3
              class="mb-3 text-sm font-medium text-gray-900 flex items-center"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 text-indigo-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
              Legenda
            </h3>
            <div class="flex flex-wrap gap-4">
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-green-100 border-l-4 border-green-500 mr-2"
                ></span>
                <span class="text-sm text-gray-600">Revisão realizada</span>
              </div>
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-red-100 border-l-4 border-red-500 mr-2"
                ></span>
                <span class="text-sm text-gray-600">Revisão não realizada</span>
              </div>
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-yellow-100 border-l-4 border-yellow-400 mr-2"
                ></span>
                <span class="text-sm text-gray-600">Revisão para hoje</span>
              </div>
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-gray-100 border-l-4 border-gray-400 mr-2"
                ></span>
                <span class="text-sm text-gray-600">Revisão futura</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Calendar from "@/Components/Calendar.vue";

const props = defineProps({
  calendarData: Object,
  month: Number,
  year: Number,
});
</script>