<template>
  <Head title="Calendário" />

  <AuthenticatedLayout>
    <template #header>
      <div
        class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
      >
        <h2
          class="text-xl font-semibold leading-tight text-gray-800 dark:text-white"
        >
          Calendário de Revisões
        </h2>
        <div
          class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3"
        >
          <Link
            :href="route('revisions.index')"
            class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
          >
            <i class="ri-task-line -ml-1 mr-2"></i>
            <span class="hidden sm:inline">Ver Lista de Revisões</span>
            <span class="sm:hidden">Revis<PERSON>es</span>
          </Link>
          <Link
            :href="route('lessons.create')"
            class="inline-flex items-center justify-center rounded-md border border-indigo-600 bg-white px-4 py-2 text-sm font-medium text-indigo-700 shadow-sm hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors dark:bg-gray-800 dark:border-indigo-500 dark:text-indigo-400 dark:hover:bg-gray-700"
          >
            <i class="ri-add-line -ml-1 mr-2"></i>
            <span class="hidden sm:inline">Nova Aula</span>
            <span class="sm:hidden">Nova Aula</span>
          </Link>
        </div>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Calendar Header -->
        <div
          class="mb-4 sm:mb-6 flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
        >
          <h3
            class="text-base sm:text-lg font-medium text-gray-900 dark:text-white"
          >
            Visualize suas revisões agendadas
          </h3>
          <div class="flex items-center space-x-4">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{
                new Date(year, month - 1).toLocaleDateString("pt-BR", {
                  month: "long",
                  year: "numeric",
                })
              }}
            </span>
          </div>
        </div>

        <!-- Calendar Container -->
        <div
          class="overflow-hidden rounded-lg bg-white shadow-md border border-gray-100 dark:bg-gray-800 dark:border-gray-700"
        >
          <div class="p-3 sm:p-6">
            <Calendar
              :calendar-data="calendarData"
              :month="month"
              :year="year"
            />
          </div>
        </div>

        <!-- Legend -->
        <div
          class="mt-4 sm:mt-6 overflow-hidden rounded-lg bg-white shadow-md border border-gray-100 dark:bg-gray-800 dark:border-gray-700"
        >
          <div class="p-3 sm:p-4">
            <h3
              class="mb-3 text-sm font-medium text-gray-900 dark:text-white flex items-center"
            >
              <i
                class="ri-information-line mr-2 text-indigo-600 dark:text-indigo-400"
              ></i>
              Legenda
            </h3>
            <div
              class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4"
            >
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-green-100 border-l-4 border-green-500 mr-2 flex-shrink-0"
                ></span>
                <span
                  class="text-xs sm:text-sm text-gray-600 dark:text-gray-300"
                  >Revisão realizada</span
                >
              </div>
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-red-100 border-l-4 border-red-500 mr-2 flex-shrink-0"
                ></span>
                <span
                  class="text-xs sm:text-sm text-gray-600 dark:text-gray-300"
                  >Revisão não realizada</span
                >
              </div>
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-yellow-100 border-l-4 border-yellow-400 mr-2 flex-shrink-0"
                ></span>
                <span
                  class="text-xs sm:text-sm text-gray-600 dark:text-gray-300"
                  >Revisão para hoje</span
                >
              </div>
              <div class="flex items-center">
                <span
                  class="h-4 w-4 rounded-md bg-gray-100 border-l-4 border-gray-400 mr-2 flex-shrink-0"
                ></span>
                <span
                  class="text-xs sm:text-sm text-gray-600 dark:text-gray-300"
                  >Revisão futura</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Calendar from "@/Components/Calendar.vue";

const props = defineProps({
  calendarData: Object,
  month: Number,
  year: Number,
});
</script>