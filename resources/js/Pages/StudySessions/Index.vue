<template>
  <Head title="Sessões de Estudo" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2
          class="text-lg sm:text-xl font-semibold leading-tight text-gray-800 dark:text-white"
        >
          Se<PERSON><PERSON><PERSON> de Estudo
        </h2>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
          <!-- Timer Panel -->
          <div class="md:col-span-1">
            <StudySessionTimer
              :disciplines="disciplines"
              :discipline-groups="disciplineGroups"
              @session-ended="handleSessionEnded"
            />
          </div>

          <!-- Chart Panel -->
          <div class="md:col-span-2">
            <div
              class="bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-md border border-gray-100 dark:border-gray-700"
            >
              <h3
                class="text-base sm:text-lg font-medium text-gray-900 dark:text-white mb-3 sm:mb-4"
              >
                Tempo de Estudo por Disciplina
              </h3>
              <StudySessionChart :chart-data="chartData" />
            </div>
          </div>
        </div>

        <!-- Study Sessions History -->
        <div
          class="mt-4 sm:mt-6 bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-lg shadow-md border border-gray-100 dark:border-gray-700"
        >
          <h3
            class="text-base sm:text-lg font-medium text-gray-900 dark:text-white mb-3 sm:mb-4"
          >
            Histórico de Sessões de Estudo
          </h3>

          <div
            v-if="studySessions.length === 0"
            class="text-center py-6 sm:py-8"
          >
            <div class="text-gray-500 dark:text-gray-400">
              <i class="ri-time-line text-3xl sm:text-4xl mb-2"></i>
              <p class="text-sm sm:text-base">
                Você ainda não tem sessões de estudo registradas.
              </p>
              <p class="text-xs sm:text-sm mt-2">
                Selecione uma disciplina e inicie uma sessão de estudo para
                começar a registrar seu tempo.
              </p>
            </div>
          </div>

          <div v-else>
            <!-- Mobile Cards (visible on small screens) -->
            <div class="block sm:hidden space-y-3">
              <div
                v-for="session in studySessions"
                :key="session.id"
                class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600"
              >
                <div class="flex items-center justify-between mb-2">
                  <h4
                    class="text-sm font-medium text-gray-900 dark:text-white truncate"
                  >
                    {{ session.discipline.name }}
                  </h4>
                  <span
                    class="text-xs font-medium text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/50 px-2 py-1 rounded"
                  >
                    {{ formatDuration(session.duration_seconds) }}
                  </span>
                </div>
                <div
                  class="grid grid-cols-2 gap-2 text-xs text-gray-500 dark:text-gray-400"
                >
                  <div>
                    <span class="font-medium">Data:</span>
                    {{ formatDate(session.started_at) }}
                  </div>
                  <div>
                    <span class="font-medium">Início:</span>
                    {{ formatTime(session.started_at) }}
                  </div>
                  <div>
                    <span class="font-medium">Fim:</span>
                    {{ formatTime(session.ended_at) }}
                  </div>
                  <div class="col-span-2" v-if="session.notes">
                    <span class="font-medium">Anotações:</span>
                    <p class="mt-1 text-gray-600 dark:text-gray-300 text-xs">
                      {{ session.notes }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Desktop Table (hidden on small screens) -->
            <div class="hidden sm:block overflow-x-auto">
              <table
                class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"
              >
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Disciplina
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Data
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Início
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Fim
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Duração
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                    >
                      Anotações
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
                >
                  <tr
                    v-for="session in studySessions"
                    :key="session.id"
                    class="hover:bg-gray-50 dark:hover:bg-gray-700/50"
                  >
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div
                        class="text-sm font-medium text-gray-900 dark:text-white"
                      >
                        {{ session.discipline.name }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ formatDate(session.started_at) }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ formatTime(session.started_at) }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ formatTime(session.ended_at) }}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        {{ formatDuration(session.duration_seconds) }}
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div
                        class="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate"
                      >
                        {{ session.notes || "-" }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head } from "@inertiajs/vue3";
import { ref } from "vue";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import StudySessionTimer from "@/Components/StudySessionTimer.vue";
import StudySessionChart from "@/Components/StudySessionChart.vue";

const props = defineProps({
  studySessions: Array,
  disciplines: Array,
  disciplineGroups: Array,
  chartData: Object,
});

const handleSessionEnded = (session) => {
  // Reload the page to refresh the data
  window.location.reload();
};

const formatDate = (dateString) => {
  if (!dateString) return "-";
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

const formatTime = (dateString) => {
  if (!dateString) return "-";
  const options = { hour: "2-digit", minute: "2-digit" };
  return new Date(dateString).toLocaleTimeString("pt-BR", options);
};

const formatDuration = (seconds) => {
  if (!seconds) return "-";

  // Ensure seconds is a positive number
  seconds = Math.abs(seconds);

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};
</script>
