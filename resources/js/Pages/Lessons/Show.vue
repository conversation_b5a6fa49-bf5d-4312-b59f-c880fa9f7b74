<template>
  <Head :title="lesson.title" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="h-6 w-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"
              />
            </svg>
          </div>
          <div>
            <h2
              class="text-xl font-semibold leading-tight text-indigo-800 dark:text-white"
            >
              {{ lesson.title }}
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ lesson.discipline.name }}
            </p>
          </div>
        </div>
        <div class="flex space-x-2">
          <Link
            :href="route('lessons.index')"
            class="flex items-center rounded-lg border border-transparent bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 transition-colors hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-950/50 dark:text-indigo-300 dark:hover:bg-indigo-950/70"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="mr-1.5 h-4 w-4"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"
              />
            </svg>
            Voltar
          </Link>
          <div class="flex space-x-2">
            <Link
              :href="route('lessons.edit', lesson.id)"
              class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-1.5 h-4 w-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                />
              </svg>
              Editar
            </Link>
            <button
              @click="confirmDeleteLesson"
              class="flex items-center rounded-lg bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:bg-red-700 dark:hover:bg-red-600"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-1.5 h-4 w-4"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                />
              </svg>
              Excluir
            </button>
          </div>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Lesson Info -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Informações da Aula"
              v-model="sectionsExpanded.lessonInfo"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"
                  />
                </svg>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.lessonInfo" class="px-6 py-5">
            <div
              class="mb-6 rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
            >
              <h3
                class="mb-4 text-lg font-medium text-gray-900 dark:text-white"
              >
                Roteiro de Estudo
              </h3>
              <ul>
                <li>1º Revisar os flashcards</li>
                <li>
                  2º Ler o mapa mental e gravar, caso ainda não tenha feito
                </li>
                <li>
                  3º Ouvir os áudios de revisão, se já estiverem disponíveis
                </li>
                <li>4º Responder as questões de revisão</li>
                <li>
                  5º Resolver 10 questões do tema, acessando os Links de
                  Questões
                </li>
              </ul>
            </div>
            <dl class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2">
              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4 text-indigo-500"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"
                    />
                  </svg>
                  Título
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  {{ lesson.title }}
                </dd>
              </div>

              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4 text-indigo-500"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-********* 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5"
                    />
                  </svg>
                  Disciplina
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  <Link
                    :href="route('disciplines.show', lesson.discipline.id)"
                    class="flex items-center text-indigo-600 transition-colors hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
                  >
                    {{ lesson.discipline.name }}
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="ml-1 h-3.5 w-3.5"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
                      />
                    </svg>
                  </Link>
                </dd>
              </div>

              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4 text-indigo-500"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
                    />
                  </svg>
                  Data de Estudo
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  {{ formatDate(lesson.study_date) }}
                </dd>
              </div>

              <div
                class="sm:col-span-2 rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4 text-indigo-500"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25H12"
                    />
                  </svg>
                  Descrição
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  {{ lesson.description || "Sem descrição" }}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Mind Map -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Mapa Mental"
              v-model="sectionsExpanded.mindMap"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 3v11.25A2.25 2.25 0 006 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0118 16.5h-2.25m-7.5 0h7.5m-7.5 0l-1 3m8.5-3l1 3m0 0l.5 1.5m-.5-1.5h-9.5m0 0l-.5 1.5m.75-9l3-3 2.148 2.148A12.061 12.061 0 0116.5 7.605"
                  />
                </svg>
              </template>
              <template #actions>
                <div v-if="!lesson.mind_map">
                  <Link
                    :href="route('mind-maps.create', { lesson_id: lesson.id })"
                    class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="mr-1.5 h-4 w-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 4.5v15m7.5-7.5h-15"
                      />
                    </svg>
                    Criar Mapa Mental
                  </Link>
                </div>
                <div v-else class="flex space-x-2">
                  <Link
                    :href="route('mind-maps.edit', lesson.mind_map.id)"
                    class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="mr-1.5 h-4 w-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                      />
                    </svg>
                    Editar Mapa Mental
                  </Link>
                </div>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.mindMap" class="px-6 py-5">
            <div
              v-if="!lesson.mind_map"
              class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 text-center dark:border-gray-700 dark:bg-zinc-800/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mb-4 h-12 w-12 text-gray-400 dark:text-gray-500"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M2.25 15.75l5.159-5.159a2.25 2.25 0 013.182 0l5.159 5.159m-1.5-1.5l1.409-1.409a2.25 2.25 0 013.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 001.5-1.5V6a1.5 1.5 0 00-1.5-1.5H3.75A1.5 1.5 0 002.25 6v12a1.5 1.5 0 001.5 1.5zm10.5-11.25h.008v.008h-.008V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                />
              </svg>
              <p
                class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"
              >
                Nenhum mapa mental criado para esta aula
              </p>
              <p class="mb-4 text-xs text-gray-500 dark:text-gray-500">
                Crie um mapa mental para organizar os conceitos desta aula
              </p>
              <Link
                :href="route('mind-maps.create', { lesson_id: lesson.id })"
                class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-1.5 h-4 w-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M12 4.5v15m7.5-7.5h-15"
                  />
                </svg>
                Criar Mapa Mental
              </Link>
            </div>
            <div
              v-else
              class="flex justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-zinc-800"
            >
              <div class="w-full max-w-4xl">
                <MindMapPreview :data="{ content: lesson.mind_map.content }" />
              </div>
            </div>
          </div>
        </div>

        <!-- Question Links -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Links de Questões"
              v-model="sectionsExpanded.questionLinks"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244"
                  />
                </svg>
              </template>
              <template #actions>
                <button
                  @click="showAddLinkModal = true"
                  class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M12 4.5v15m7.5-7.5h-15"
                    />
                  </svg>
                  Adicionar Link
                </button>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.questionLinks" class="px-6 py-5">
            <div
              v-if="lesson.question_links.length === 0"
              class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 text-center dark:border-gray-700 dark:bg-zinc-800/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mb-4 h-12 w-12 text-gray-400 dark:text-gray-500"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244"
                />
              </svg>
              <p
                class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"
              >
                Nenhum link de questões adicionado para esta aula
              </p>
              <p class="mb-4 text-xs text-gray-500 dark:text-gray-500">
                Adicione links para questões relacionadas ao conteúdo desta aula
              </p>
              <button
                @click="showAddLinkModal = true"
                class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-1.5 h-4 w-4"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M12 4.5v15m7.5-7.5h-15"
                  />
                </svg>
                Adicionar Primeiro Link
              </button>
            </div>
            <div v-else>
              <ul class="space-y-3">
                <li
                  v-for="link in lesson.question_links"
                  :key="link.id"
                  class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-700 dark:bg-zinc-800"
                >
                  <div class="p-4">
                    <div
                      class="flex flex-col sm:flex-row sm:items-center sm:justify-between"
                    >
                      <div class="mb-3 sm:mb-0">
                        <a
                          :href="link.url"
                          target="_blank"
                          rel="noopener noreferrer"
                          class="truncate text-xs text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
                        >
                          <h4 class="text-base font-medium">
                            <i class="ri-survey-line"></i>
                            {{ link.title }}
                          </h4>
                        </a>
                      </div>
                      <div class="flex space-x-2">
                        <button
                          @click="editLink(link)"
                          class="flex items-center rounded-lg bg-gray-100 px-3 py-1.5 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                            />
                          </svg>
                          Editar
                        </button>
                        <a
                          :href="link.url"
                          target="_blank"
                          rel="noopener noreferrer"
                          class="flex items-center rounded-lg bg-indigo-50 px-3 py-1.5 text-xs font-medium text-indigo-700 transition-colors hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-900/30 dark:text-indigo-300 dark:hover:bg-indigo-900/50"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M13.5 6H5.25A2.25 2.25 0 003 8.25v10.5A2.25 2.25 0 005.25 21h10.5A2.25 2.25 0 0018 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25"
                            />
                          </svg>
                          Abrir
                        </a>
                        <button
                          @click="confirmDeleteLink(link)"
                          class="flex items-center rounded-lg bg-red-50 px-3 py-1.5 text-xs font-medium text-red-700 transition-colors hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                            />
                          </svg>
                          Excluir
                        </button>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Audio Recordings -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Áudios de Revisão"
              v-model="sectionsExpanded.audioRecordings"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"
                  />
                </svg>
              </template>
              <template #actions>
                <div class="flex space-x-2">
                  <button
                    @click="showUploadModal = true"
                    class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="mr-1.5 h-4 w-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
                      />
                    </svg>
                    Upload MP3
                  </button>
                  <button
                    @click="showRecorderModal = true"
                    class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      class="mr-1.5 h-4 w-4"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"
                      />
                    </svg>
                    Gravar Áudio
                  </button>
                </div>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.audioRecordings" class="px-6 py-5">
            <div
              v-if="lesson.audio_recordings.length === 0"
              class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 text-center dark:border-gray-700 dark:bg-zinc-800/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mb-4 h-12 w-12 text-gray-400 dark:text-gray-500"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"
                />
              </svg>
              <p
                class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"
              >
                Nenhum áudio de revisão para esta aula
              </p>
              <p class="mb-4 text-xs text-gray-500 dark:text-gray-500">
                Grave ou faça upload de áudios para revisar o conteúdo desta
                aula mais tarde
              </p>
              <div class="flex space-x-2">
                <button
                  @click="showUploadModal = true"
                  class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5m-13.5-9L12 3m0 0l4.5 4.5M12 3v13.5"
                    />
                  </svg>
                  Upload MP3
                </button>
                <button
                  @click="showRecorderModal = true"
                  class="flex items-center rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="mr-1.5 h-4 w-4"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M12 18.75a6 6 0 006-6v-1.5m-6 7.5a6 6 0 01-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 01-3-3V4.5a3 3 0 116 0v8.25a3 3 0 01-3 3z"
                    />
                  </svg>
                  Gravar Áudio
                </button>
              </div>
            </div>
            <div v-else>
              <ul class="space-y-4">
                <li
                  v-for="audio in lesson.audio_recordings"
                  :key="audio.id"
                  class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-700 dark:bg-zinc-800"
                >
                  <div
                    class="border-b border-gray-200 bg-gray-50 px-4 py-3 dark:border-gray-700 dark:bg-zinc-800/80"
                  >
                    <div class="flex items-center justify-between">
                      <div>
                        <h4
                          class="text-sm font-medium text-gray-900 dark:text-gray-100"
                        >
                          {{ audio.title }}
                        </h4>
                        <p
                          v-if="audio.duration_seconds"
                          class="mt-1 text-xs text-gray-500 dark:text-gray-400"
                        >
                          Duração: {{ formatDuration(audio.duration_seconds) }}
                        </p>
                      </div>
                      <div class="flex space-x-2">
                        <button
                          @click="editAudio(audio)"
                          class="flex items-center rounded-lg bg-gray-100 px-3 py-1.5 text-xs font-medium text-gray-700 transition-colors hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                            />
                          </svg>
                          Editar
                        </button>
                        <button
                          @click="confirmDeleteAudio(audio)"
                          class="flex items-center rounded-lg bg-red-50 px-3 py-1.5 text-xs font-medium text-red-700 transition-colors hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:bg-red-900/30 dark:text-red-300 dark:hover:bg-red-900/50"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                            />
                          </svg>
                          Excluir
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Player de áudio personalizado -->
                  <div class="custom-audio-player p-4">
                    <div class="flex items-center space-x-4">
                      <!-- Botão de play/pause -->
                      <button
                        @click="togglePlay(audio)"
                        class="play-button flex h-12 w-12 items-center justify-center rounded-full bg-indigo-600 text-white shadow-md transition-all duration-200 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                      >
                        <svg
                          v-if="!isPlaying[audio.id]"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-6 w-6"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                        <svg
                          v-else
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-6 w-6"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        >
                          <rect x="6" y="4" width="4" height="16"></rect>
                          <rect x="14" y="4" width="4" height="16"></rect>
                        </svg>
                      </button>

                      <div class="flex-grow">
                        <!-- Barra de progresso -->
                        <div
                          class="relative h-2.5 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"
                        >
                          <div
                            class="absolute top-0 left-0 h-full rounded-full bg-indigo-600 transition-all duration-100 dark:bg-indigo-500"
                            :style="{
                              width: `${audioProgress[audio.id] || 0}%`,
                            }"
                          ></div>
                        </div>

                        <!-- Tempo atual / Duração total -->
                        <div
                          class="mt-1.5 flex justify-between text-xs text-gray-500 dark:text-gray-400"
                        >
                          <span>{{
                            formatTime(audioCurrentTime[audio.id] || 0)
                          }}</span>
                          <span>{{
                            formatDuration(audio.duration_seconds)
                          }}</span>
                        </div>
                      </div>

                      <!-- Controles de áudio -->
                      <div class="audio-controls flex items-center space-x-4">
                        <!-- Controle de velocidade -->
                        <div class="playback-speed-control relative">
                          <button
                            @click="toggleSpeedMenu(audio)"
                            class="flex items-center text-xs font-medium text-gray-600 hover:text-gray-800 focus:outline-none dark:text-gray-400 dark:hover:text-gray-300"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              class="h-4 w-4 mr-1"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <circle cx="12" cy="12" r="10"></circle>
                              <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            {{ audioPlaybackSpeed[audio.id] || "1.0" }}x
                          </button>

                          <!-- Menu de velocidade -->
                          <div
                            v-if="speedMenuOpen[audio.id]"
                            class="absolute bottom-full left-0 mb-2 w-24 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-zinc-800 dark:ring-white/10 z-10"
                          >
                            <div
                              class="py-1"
                              role="menu"
                              aria-orientation="vertical"
                            >
                              <button
                                v-for="speed in [
                                  0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0,
                                ]"
                                :key="speed"
                                @click="changePlaybackSpeed(audio, speed)"
                                class="block w-full px-4 py-2 text-left text-xs text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-zinc-700"
                                :class="{
                                  'font-bold text-indigo-600 dark:text-indigo-400':
                                    (audioPlaybackSpeed[audio.id] || 1.0) ===
                                    speed,
                                }"
                                role="menuitem"
                              >
                                {{ speed }}x
                              </button>
                            </div>
                          </div>
                        </div>

                        <!-- Controle de volume -->
                        <div class="volume-control flex items-center space-x-2">
                          <button
                            @click="toggleMute(audio)"
                            class="text-gray-600 hover:text-gray-800 focus:outline-none dark:text-gray-400 dark:hover:text-gray-300"
                          >
                            <svg
                              v-if="!isMuted[audio.id]"
                              xmlns="http://www.w3.org/2000/svg"
                              class="h-5 w-5"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <polygon
                                points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"
                              ></polygon>
                              <path
                                d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"
                              ></path>
                            </svg>
                            <svg
                              v-else
                              xmlns="http://www.w3.org/2000/svg"
                              class="h-5 w-5"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            >
                              <polygon
                                points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"
                              ></polygon>
                              <line x1="23" y1="9" x2="17" y2="15"></line>
                              <line x1="17" y1="9" x2="23" y2="15"></line>
                            </svg>
                          </button>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            :value="audioVolume[audio.id] || 100"
                            @input="updateVolume($event, audio)"
                            class="h-1.5 w-20 cursor-pointer appearance-none rounded-lg bg-gray-300 dark:bg-gray-600"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- Elemento de áudio oculto para controle via JavaScript -->
                    <audio
                      :ref="
                        (el) => {
                          if (el) audioElements[audio.id] = el;
                        }
                      "
                      :src="audio.audio_url"
                      preload="metadata"
                      @timeupdate="updateProgress(audio)"
                      @ended="audioEnded(audio)"
                      class="hidden"
                    ></audio>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Notes -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader title="Anotações" v-model="sectionsExpanded.notes">
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
                  />
                </svg>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.notes" class="px-6 py-5">
            <NotesList
              :notes="lesson.notes"
              :lesson-id="lesson.id"
              :can-edit="true"
            />
          </div>
        </div>

        <!-- Flashcards -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Flashcards"
              v-model="sectionsExpanded.flashcards"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6A2.25 2.25 0 016 3.75h3.879a1.5 1.5 0 011.06.44l2.122 2.12a1.5 1.5 0 001.06.44H18A2.25 2.25 0 0120.25 9v.776"
                  />
                </svg>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.flashcards" class="px-6 py-5">
            <FlashcardList
              :flashcards="lesson.flashcards"
              :lesson-id="lesson.id"
              :can-edit="true"
            />
          </div>
        </div>

        <!-- Questions -->
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Questões"
              v-model="sectionsExpanded.questions"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z"
                  />
                </svg>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.questions" class="px-6 py-5">
            <QuestionList
              :questions="lesson.questions"
              :lesson-id="lesson.id"
              :can-edit="true"
            />
          </div>
        </div>

        <!-- Revisions -->
        <div
          class="overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Cronograma de Revisões"
              v-model="sectionsExpanded.revisions"
            >
              <template #icon>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z"
                  />
                </svg>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.revisions" class="px-6 py-5">
            <div
              v-if="lesson.revisions.length === 0"
              class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 py-12 text-center dark:border-gray-700 dark:bg-zinc-800/50"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mb-4 h-12 w-12 text-gray-400 dark:text-gray-500"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
                />
              </svg>
              <p
                class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"
              >
                Nenhuma revisão agendada para esta aula
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-500">
                As revisões são criadas automaticamente quando você adiciona uma
                aula
              </p>
            </div>
            <div v-else>
              <ul class="space-y-3">
                <li
                  v-for="revision in lesson.revisions"
                  :key="revision.id"
                  class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-700 dark:bg-zinc-800"
                >
                  <div class="p-4">
                    <div
                      class="flex flex-col sm:flex-row sm:items-center sm:justify-between"
                    >
                      <div class="mb-3 sm:mb-0">
                        <div class="flex items-center">
                          <div
                            class="mr-2 flex h-8 w-8 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900/50 dark:text-indigo-400"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke-width="1.5"
                              stroke="currentColor"
                              class="h-4 w-4"
                            >
                              <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                            </svg>
                          </div>
                          <h4
                            class="text-base font-medium text-gray-900 dark:text-gray-100"
                          >
                            {{ getRevisionTypeLabel(revision.revision_type) }}
                          </h4>
                        </div>
                        <div
                          class="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5"
                            />
                          </svg>
                          {{ formatDate(revision.scheduled_date) }}
                        </div>
                      </div>
                      <div class="flex items-center space-x-3">
                        <span
                          :class="{
                            'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400':
                              revision.is_completed,
                            'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400':
                              !revision.is_completed,
                          }"
                          class="inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium"
                        >
                          <svg
                            v-if="revision.is_completed"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3 w-3"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M4.5 12.75l6 6 9-13.5"
                            />
                          </svg>
                          <svg
                            v-else
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3 w-3"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          {{ revision.is_completed ? "Concluída" : "Pendente" }}
                        </span>
                        <Link
                          :href="route('revisions.show', revision.id)"
                          class="flex items-center rounded-lg bg-indigo-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm transition-colors hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke-width="1.5"
                            stroke="currentColor"
                            class="mr-1 h-3.5 w-3.5"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"
                            />
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          {{
                            revision.is_completed
                              ? "Ver Detalhes"
                              : "Realizar Revisão"
                          }}
                        </Link>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Link Modal -->
    <Modal :show="showAddLinkModal" @close="closeAddLinkModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          {{ editingLink ? "Editar Link" : "Adicionar Link de Questões" }}
        </h2>
        <form @submit.prevent="submitLink" class="mt-4">
          <div class="mb-4">
            <InputLabel for="link_title" value="Título" />
            <TextInput
              id="link_title"
              type="text"
              class="mt-1 block w-full"
              v-model="linkForm.title"
              required
              autofocus
            />
            <InputError class="mt-2" :message="linkForm.errors.title" />
          </div>

          <div class="mb-4">
            <InputLabel for="link_url" value="URL" />
            <TextInput
              id="link_url"
              type="url"
              class="mt-1 block w-full"
              v-model="linkForm.url"
              required
              placeholder="https://exemplo.com"
            />
            <InputError class="mt-2" :message="linkForm.errors.url" />
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <SecondaryButton @click="closeAddLinkModal"
              >Cancelar</SecondaryButton
            >
            <PrimaryButton
              :class="{ 'opacity-25': linkForm.processing }"
              :disabled="linkForm.processing"
            >
              {{ editingLink ? "Atualizar" : "Adicionar" }}
            </PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Audio Recorder Modal -->
    <Modal :show="showRecorderModal" @close="closeRecorderModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">
          Gravar Áudio de Revisão
        </h2>
        <div class="mt-4">
          <div v-if="!audioRecorded">
            <AudioRecorder @save="saveAudioRecording" />
          </div>
          <div v-else>
            <div class="mb-4">
              <InputLabel for="audio_title" value="Título do Áudio" />
              <TextInput
                id="audio_title"
                type="text"
                class="mt-1 block w-full"
                v-model="audioForm.title"
                required
                autofocus
              />
              <InputError class="mt-2" :message="audioForm.errors.title" />
            </div>

            <div class="mt-6 flex justify-end space-x-3">
              <SecondaryButton @click="closeRecorderModal"
                >Cancelar</SecondaryButton
              >
              <PrimaryButton
                @click="submitAudio"
                :class="{ 'opacity-25': audioForm.processing }"
                :disabled="audioForm.processing"
              >
                Salvar Áudio
              </PrimaryButton>
            </div>
          </div>
        </div>
      </div>
    </Modal>

    <!-- Edit Audio Modal -->
    <Modal :show="showEditAudioModal" @close="closeEditAudioModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Editar Nome do Áudio
        </h2>
        <form @submit.prevent="submitEditAudio" class="mt-4">
          <div class="mb-4">
            <InputLabel for="edit_audio_title" value="Título do Áudio" />
            <TextInput
              id="edit_audio_title"
              type="text"
              class="mt-1 block w-full"
              v-model="editAudioForm.title"
              required
              autofocus
            />
            <InputError class="mt-2" :message="editAudioForm.errors.title" />
          </div>

          <div class="mt-6 flex justify-end space-x-3">
            <SecondaryButton @click="closeEditAudioModal"
              >Cancelar</SecondaryButton
            >
            <PrimaryButton
              :class="{ 'opacity-25': editAudioForm.processing }"
              :disabled="editAudioForm.processing"
            >
              Atualizar
            </PrimaryButton>
          </div>
        </form>
      </div>
    </Modal>

    <!-- Audio Upload Modal -->
    <AudioUploadModal
      :show="showUploadModal"
      :lesson-id="lesson.id"
      @close="showUploadModal = false"
      @uploaded="handleAudioUploaded"
    />

    <!-- Delete Confirmation Modal -->
    <Modal :show="showDeleteModal" @close="closeDeleteModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
          Confirmar Exclusão
        </h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          {{ deleteModalMessage }}
        </p>
        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeDeleteModal">Cancelar</SecondaryButton>
          <DangerButton
            @click="confirmDelete"
            :class="{ 'opacity-25': processing }"
            :disabled="processing"
          >
            Excluir
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, Link, router, useForm } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Modal from "@/Components/Modal.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import DangerButton from "@/Components/DangerButton.vue";
import TextInput from "@/Components/TextInput.vue";
import AudioRecorder from "@/Components/AudioRecorder.vue";
import AudioUploadModal from "@/Components/AudioUploadModal.vue";
import MindMapPreview from "@/Components/MindMapPreview.vue";
import QuestionList from "@/Components/QuestionList.vue";
import FlashcardList from "@/Components/FlashcardList.vue";
import NotesList from "@/Components/NotesList.vue";
import SectionHeader from "@/Components/SectionHeader.vue";

const props = defineProps({
  lesson: Object,
});

// Section expansion state
const sectionsExpanded = ref({
  lessonInfo: true, // Informações da Aula - expanded by default
  mindMap: false, // Mapa Mental
  questionLinks: false, // Links de Questões
  audioRecordings: false, // Áudios de Revisão
  notes: false, // Anotações
  flashcards: false, // Flashcards
  questions: false, // Questões
  revisions: false, // Cronograma de Revisões
});

// Format utilities
const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

const formatDuration = (seconds) => {
  if (!seconds) return "00:00";
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

// Alias para formatDuration para uso no player de áudio
const formatTime = formatDuration;

const getRevisionTypeLabel = (type) => {
  const labels = {
    first: "1ª Revisão",
    second: "2ª Revisão",
    third: "3ª Revisão",
    fourth: "4ª Revisão",
    recurring: "Revisão Recorrente",
    weekly: "Revisão Semanal",
  };

  return labels[type] || "Revisão";
};

// Question Links
const showAddLinkModal = ref(false);
const editingLink = ref(null);
const linkForm = useForm({
  title: "",
  url: "",
  lesson_id: props.lesson.id,
});

const closeAddLinkModal = () => {
  showAddLinkModal.value = false;
  setTimeout(() => {
    linkForm.reset();
    editingLink.value = null;
  }, 300);
};

const editLink = (link) => {
  editingLink.value = link;
  linkForm.title = link.title;
  linkForm.url = link.url;
  showAddLinkModal.value = true;
};

const submitLink = () => {
  if (editingLink.value) {
    linkForm.put(route("question-links.update", editingLink.value.id), {
      onSuccess: () => closeAddLinkModal(),
    });
  } else {
    linkForm.post(route("question-links.store"), {
      onSuccess: () => closeAddLinkModal(),
    });
  }
};

// Audio Recordings
const showRecorderModal = ref(false);
const showUploadModal = ref(false);
const showEditAudioModal = ref(false);
const audioRecorded = ref(false);
const audioData = ref(null);
const editingAudio = ref(null);
const audioForm = useForm({
  title: "",
  audio_file: null,
  duration_seconds: null,
  lesson_id: props.lesson.id,
});
const editAudioForm = useForm({
  title: "",
});

// Player de áudio personalizado
const audioElements = ref({});
const isPlaying = ref({});
const isMuted = ref({});
const audioProgress = ref({});
const audioCurrentTime = ref({});
const audioVolume = ref({});
const audioPlaybackSpeed = ref({});
const speedMenuOpen = ref({});

// Função para alternar entre play e pause
const togglePlay = (audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  if (isPlaying.value[audio.id]) {
    audioElement.pause();
    isPlaying.value[audio.id] = false;
  } else {
    // Pausar todos os outros áudios que estejam tocando
    Object.keys(audioElements.value).forEach((id) => {
      if (id !== audio.id.toString() && audioElements.value[id]) {
        audioElements.value[id].pause();
        isPlaying.value[id] = false;
      }
    });

    audioElement.play().catch((error) => {
      console.error("Erro ao reproduzir áudio:", error);
    });
    isPlaying.value[audio.id] = true;
  }
};

// Função para atualizar o progresso da reprodução
const updateProgress = (audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  const progress = (audioElement.currentTime / audioElement.duration) * 100;
  audioProgress.value[audio.id] = progress;
  audioCurrentTime.value[audio.id] = audioElement.currentTime;
};

// Função para lidar com o fim da reprodução
const audioEnded = (audio) => {
  isPlaying.value[audio.id] = false;
  audioProgress.value[audio.id] = 0;
  audioCurrentTime.value[audio.id] = 0;
};

// Função para alternar entre mudo e com som
const toggleMute = (audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  audioElement.muted = !audioElement.muted;
  isMuted.value[audio.id] = audioElement.muted;
};

// Função para atualizar o volume
const updateVolume = (event, audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  const volume = event.target.value / 100;
  audioElement.volume = volume;
  audioVolume.value[audio.id] = event.target.value;

  // Se o volume for 0, considerar como mudo
  if (volume === 0) {
    isMuted.value[audio.id] = true;
  } else if (isMuted.value[audio.id]) {
    isMuted.value[audio.id] = false;
    audioElement.muted = false;
  }
};

// Função para alternar o menu de velocidade
const toggleSpeedMenu = (audio) => {
  // Fechar todos os outros menus de velocidade
  Object.keys(speedMenuOpen.value).forEach((id) => {
    if (id !== audio.id.toString()) {
      speedMenuOpen.value[id] = false;
    }
  });

  // Alternar o menu atual
  speedMenuOpen.value[audio.id] = !speedMenuOpen.value[audio.id];

  // Adicionar um event listener para fechar o menu quando clicar fora dele
  if (speedMenuOpen.value[audio.id]) {
    setTimeout(() => {
      const closeMenu = (e) => {
        const speedMenus = document.querySelectorAll(".playback-speed-control");
        let clickedInside = false;

        speedMenus.forEach((menu) => {
          if (menu.contains(e.target)) {
            clickedInside = true;
          }
        });

        if (!clickedInside) {
          speedMenuOpen.value[audio.id] = false;
          document.removeEventListener("click", closeMenu);
        }
      };

      document.addEventListener("click", closeMenu);
    }, 0);
  }
};

// Função para alterar a velocidade de reprodução
const changePlaybackSpeed = (audio, speed) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  audioElement.playbackRate = speed;
  audioPlaybackSpeed.value[audio.id] = speed;
  speedMenuOpen.value[audio.id] = false;
};

const closeRecorderModal = () => {
  showRecorderModal.value = false;
  setTimeout(() => {
    audioForm.reset();
    audioRecorded.value = false;
    audioData.value = null;
  }, 300);
};

const editAudio = (audio) => {
  editingAudio.value = audio;
  editAudioForm.title = audio.title;
  showEditAudioModal.value = true;
};

const closeEditAudioModal = () => {
  showEditAudioModal.value = false;
  setTimeout(() => {
    editAudioForm.reset();
    editingAudio.value = null;
  }, 300);
};

const submitEditAudio = () => {
  if (!editingAudio.value) return;

  editAudioForm.put(route("audio-recordings.update", editingAudio.value.id), {
    onSuccess: () => {
      closeEditAudioModal();
    },
    onError: (errors) => {
      console.error("Erro ao atualizar áudio:", errors);
    },
  });
};

const saveAudioRecording = (data) => {
  audioData.value = data;
  audioRecorded.value = true;

  // Garantir que o arquivo seja atribuído corretamente
  audioForm.audio_file = data.file;
  audioForm.duration_seconds = data.duration;
  audioForm.title = `Gravação ${new Date().toLocaleString("pt-BR")}`;
};

const submitAudio = () => {
  // Criar um FormData para enviar o arquivo
  const formData = new FormData();
  formData.append("title", audioForm.title);
  formData.append("audio_file", audioForm.audio_file);
  formData.append("duration_seconds", audioForm.duration_seconds);
  formData.append("lesson_id", props.lesson.id);

  // Usar o método post do router diretamente com FormData
  router.post(route("audio-recordings.store"), formData, {
    forceFormData: true,
    onSuccess: () => {
      closeRecorderModal();
    },
    onError: (errors) => {
      console.error("Erro ao salvar áudio:", errors);
    },
  });
};

// Handler for audio upload success
const handleAudioUploaded = () => {
  // Show a temporary success message first
  const successElement = document.createElement("div");
  successElement.className =
    "fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50";
  successElement.textContent = "Áudio enviado com sucesso!";
  document.body.appendChild(successElement);

  // Use window.location for a hard refresh to ensure everything is reloaded
  window.location.href = route("lessons.show", props.lesson.id);
};

// Delete Confirmation
const showDeleteModal = ref(false);
const deleteType = ref("");
const itemToDelete = ref(null);
const processing = ref(false);
const deleteModalMessage = ref("");

const confirmDeleteLesson = () => {
  deleteType.value = "lesson";
  itemToDelete.value = props.lesson;
  deleteModalMessage.value = `Tem certeza que deseja excluir a aula "${props.lesson.title}"? Esta ação não pode ser desfeita e todos os recursos associados (mapa mental, links, áudios e revisões) também serão excluídos.`;
  showDeleteModal.value = true;
};

const confirmDeleteLink = (link) => {
  deleteType.value = "link";
  itemToDelete.value = link;
  deleteModalMessage.value = `Tem certeza que deseja excluir o link "${link.title}"? Esta ação não pode ser desfeita.`;
  showDeleteModal.value = true;
};

const confirmDeleteAudio = (audio) => {
  deleteType.value = "audio";
  itemToDelete.value = audio;
  deleteModalMessage.value = `Tem certeza que deseja excluir o áudio "${audio.title}"? Esta ação não pode ser desfeita.`;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    deleteType.value = "";
    itemToDelete.value = null;
    deleteModalMessage.value = "";
  }, 300);
};

const confirmDelete = () => {
  if (!itemToDelete.value) return;

  processing.value = true;

  if (deleteType.value === "lesson") {
    router.delete(route("lessons.destroy", itemToDelete.value.id), {
      onSuccess: () => {
        closeDeleteModal();
        processing.value = false;
        // Redirecionar para a lista de aulas após a exclusão
        router.visit(route("lessons.index"));
      },
      onError: () => {
        processing.value = false;
      },
    });
  } else if (deleteType.value === "link") {
    router.delete(route("question-links.destroy", itemToDelete.value.id), {
      onSuccess: () => {
        closeDeleteModal();
        processing.value = false;
      },
      onError: () => {
        processing.value = false;
      },
    });
  } else if (deleteType.value === "audio") {
    router.delete(route("audio-recordings.destroy", itemToDelete.value.id), {
      onSuccess: () => {
        closeDeleteModal();
        processing.value = false;
      },
      onError: () => {
        processing.value = false;
      },
    });
  }
};
</script>

<style scoped>
/* Estilos para o player de áudio personalizado */
.custom-audio-player {
  transition: all 0.3s ease;
}

.custom-audio-player:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Estilizar o controle deslizante de volume */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 5px;
  border-radius: 5px;
  background: #e5e7eb;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: #4338ca;
  transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  background: #4338ca;
  transform: scale(1.1);
}

/* Animação para o botão de play */
.play-button {
  transition: transform 0.2s ease;
}

.play-button:hover {
  transform: scale(1.05);
}

.play-button:active {
  transform: scale(0.95);
}

/* Estilos para o controle de velocidade */
.playback-speed-control button {
  transition: all 0.2s ease;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
}

.playback-speed-control button:hover {
  background-color: #f3f4f6;
}

.dark .playback-speed-control button:hover {
  background-color: #374151;
}
</style>
