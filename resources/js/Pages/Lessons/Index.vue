<template>
  <Head title="Aulas" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">Aulas</h2>
        <Link
          :href="route('lessons.create')"
          class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          Nova Aula
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Banner informativo -->
        <div
          class="mb-8 bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-lg shadow-md overflow-hidden"
        >
          <div
            class="px-6 py-5 sm:px-8 sm:py-6 flex flex-col md:flex-row items-center justify-between"
          >
            <div class="flex items-center mb-4 md:mb-0">
              <div class="flex-shrink-0 bg-white/20 rounded-full p-2 mr-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-white">
                  Gerencie suas aulas
                </h3>
                <p class="text-indigo-100 text-sm">
                  Organize seu conteúdo de estudo e acompanhe suas revisões
                </p>
              </div>
            </div>
            <Link
              :href="route('lessons.create')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white transition-colors duration-150"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                  clip-rule="evenodd"
                />
              </svg>
              Nova Aula
            </Link>
          </div>
        </div>

        <!-- Filtros -->
        <div
          class="mb-6 bg-white rounded-lg shadow-md p-4 border border-gray-100"
        >
          <div
            class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0"
          >
            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-indigo-500 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="text-sm font-medium text-gray-700"
                  >Filtrar por disciplina:</span
                >
              </div>
              <select
                v-model="selectedDiscipline"
                @change="filterLessons"
                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              >
                <option value="">Todas as disciplinas</option>
                <option
                  v-for="discipline in props.disciplines"
                  :key="discipline.id"
                  :value="discipline.id"
                >
                  {{ discipline.name }}
                </option>
              </select>
            </div>

            <div class="flex items-center space-x-4">
              <div class="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 text-indigo-500 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z"
                  />
                </svg>
                <span class="text-sm font-medium text-gray-700"
                  >Ordenar por:</span
                >
              </div>
              <select
                v-model="sortBy"
                @change="sortLessons"
                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              >
                <option value="date_desc">Data (mais recente)</option>
                <option value="date_asc">Data (mais antiga)</option>
                <option value="title_asc">Título (A-Z)</option>
                <option value="title_desc">Título (Z-A)</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Lista de aulas -->
        <div
          class="overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
        >
          <div class="border-b border-gray-100 bg-white px-6 py-4">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2 text-indigo-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              Suas Aulas
            </h3>
          </div>
          <div class="p-6">
            <div v-if="filteredLessons.length === 0" class="text-center py-8">
              <div class="mx-auto h-24 w-24 text-indigo-300 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Nenhuma aula encontrada
              </h3>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">
                {{
                  selectedDiscipline
                    ? "Não há aulas cadastradas para esta disciplina."
                    : "Você ainda não cadastrou nenhuma aula."
                }}
              </p>
              <Link
                :href="route('lessons.create')"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                Cadastrar Primeira Aula
              </Link>
            </div>

            <div v-else>
              <ul class="divide-y divide-gray-100">
                <li
                  v-for="lesson in filteredLessons"
                  :key="lesson.id"
                  class="py-5 hover:bg-gray-50 rounded-lg transition-colors duration-150"
                >
                  <div
                    class="flex flex-col sm:flex-row sm:items-center justify-between"
                  >
                    <div class="mb-4 sm:mb-0 sm:pr-4">
                      <h4 class="text-lg font-medium text-gray-900">
                        {{ lesson.title }}
                      </h4>
                      <div
                        class="mt-2 flex flex-col sm:flex-row sm:items-center sm:space-x-4"
                      >
                        <div
                          class="flex items-center text-sm text-gray-500 mb-1 sm:mb-0"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="flex-shrink-0 mr-1.5 h-4 w-4 text-indigo-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                            />
                          </svg>
                          <span>{{ lesson.discipline.name }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="flex-shrink-0 mr-1.5 h-4 w-4 text-indigo-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                            />
                          </svg>
                          <span>{{ formatDate(lesson.study_date) }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex space-x-2">
                      <Link
                        :href="route('lessons.show', lesson.id)"
                        class="inline-flex items-center rounded-md bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-150"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 mr-1"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path
                            fill-rule="evenodd"
                            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        Ver Detalhes
                      </Link>
                      <Link
                        :href="route('lessons.edit', lesson.id)"
                        class="inline-flex items-center rounded-md bg-gray-50 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors duration-150"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 mr-1"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
                          />
                        </svg>
                        Editar
                      </Link>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  lessons: Array,
  disciplines: Array,
});

const selectedDiscipline = ref("");
const sortBy = ref("date_desc");
const filteredLessons = ref([...props.lessons]);

const filterLessons = () => {
  if (selectedDiscipline.value === "") {
    filteredLessons.value = [...props.lessons];
  } else {
    filteredLessons.value = props.lessons.filter(
      (lesson) => lesson.discipline_id === parseInt(selectedDiscipline.value)
    );
  }

  sortLessons();
};

const sortLessons = () => {
  switch (sortBy.value) {
    case "date_desc":
      filteredLessons.value.sort(
        (a, b) => new Date(b.study_date) - new Date(a.study_date)
      );
      break;
    case "date_asc":
      filteredLessons.value.sort(
        (a, b) => new Date(a.study_date) - new Date(b.study_date)
      );
      break;
    case "title_asc":
      filteredLessons.value.sort((a, b) => a.title.localeCompare(b.title));
      break;
    case "title_desc":
      filteredLessons.value.sort((a, b) => b.title.localeCompare(a.title));
      break;
  }
};

const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

onMounted(() => {
  sortLessons();
});
</script>
