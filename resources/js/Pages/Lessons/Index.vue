<template>
  <Head title="Aulas" />

  <AuthenticatedLayout>
    <template #header>
      <div
        class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
      >
        <h2
          class="text-lg sm:text-xl font-semibold leading-tight text-gray-800 dark:text-white"
        >
          Aulas
        </h2>
        <Link
          :href="route('lessons.create')"
          class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
        >
          <i class="ri-add-line mr-2 sm:mr-1"></i>
          Nova Aula
        </Link>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Banner informativo -->
        <div
          class="mb-6 sm:mb-8 bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-lg shadow-md overflow-hidden"
        >
          <div
            class="px-4 py-4 sm:px-6 sm:py-5 md:px-8 md:py-6 flex flex-col md:flex-row items-center justify-between text-center md:text-left"
          >
            <div class="flex flex-col md:flex-row items-center mb-4 md:mb-0">
              <div
                class="flex-shrink-0 bg-white/20 rounded-full p-2 mb-3 md:mb-0 md:mr-4"
              >
                <i
                  class="ri-file-list-3-line text-white text-2xl sm:text-3xl"
                ></i>
              </div>
              <div>
                <h3 class="text-base sm:text-lg font-semibold text-white">
                  Gerencie suas aulas
                </h3>
                <p class="text-indigo-100 text-sm">
                  Organize seu conteúdo de estudo e acompanhe suas revisões
                </p>
              </div>
            </div>
            <Link
              :href="route('lessons.create')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white transition-colors duration-150"
            >
              <i class="ri-add-line mr-1"></i>
              Nova Aula
            </Link>
          </div>
        </div>

        <!-- Filtros -->
        <div
          class="mb-4 sm:mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 border border-gray-100 dark:border-gray-700"
        >
          <div
            class="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0"
          >
            <div
              class="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4"
            >
              <div class="flex items-center">
                <i
                  class="ri-filter-line text-indigo-500 dark:text-indigo-400 mr-2"
                ></i>
                <span
                  class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Filtrar por disciplina:</span
                >
              </div>
              <select
                v-model="selectedDiscipline"
                @change="filterLessons"
                class="w-full sm:w-auto rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              >
                <option value="">Todas as disciplinas</option>
                <option
                  v-for="discipline in props.disciplines"
                  :key="discipline.id"
                  :value="discipline.id"
                >
                  {{ discipline.name }}
                </option>
              </select>
            </div>

            <div
              class="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4"
            >
              <div class="flex items-center">
                <i
                  class="ri-sort-desc text-indigo-500 dark:text-indigo-400 mr-2"
                ></i>
                <span
                  class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300"
                  >Ordenar por:</span
                >
              </div>
              <select
                v-model="sortBy"
                @change="sortLessons"
                class="w-full sm:w-auto rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm"
              >
                <option value="date_desc">Data (mais recente)</option>
                <option value="date_asc">Data (mais antiga)</option>
                <option value="title_asc">Título (A-Z)</option>
                <option value="title_desc">Título (Z-A)</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Lista de aulas -->
        <div
          class="overflow-hidden bg-white dark:bg-gray-800 shadow-md rounded-lg border border-gray-100 dark:border-gray-700"
        >
          <div
            class="border-b border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6 sm:py-4"
          >
            <h3
              class="text-base sm:text-lg font-medium text-gray-900 dark:text-white flex items-center"
            >
              <i
                class="ri-file-list-3-line mr-2 text-indigo-600 dark:text-indigo-400"
              ></i>
              Suas Aulas
            </h3>
          </div>
          <div class="p-4 sm:p-6">
            <div
              v-if="filteredLessons.length === 0"
              class="text-center py-6 sm:py-8"
            >
              <div
                class="mx-auto h-16 w-16 sm:h-24 sm:w-24 text-indigo-300 dark:text-indigo-600 mb-3 sm:mb-4"
              >
                <i class="ri-file-list-3-line text-6xl sm:text-8xl"></i>
              </div>
              <h3
                class="text-base sm:text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                Nenhuma aula encontrada
              </h3>
              <p
                class="text-sm sm:text-base text-gray-500 dark:text-gray-400 mb-4 sm:mb-6 max-w-md mx-auto"
              >
                {{
                  selectedDiscipline
                    ? "Não há aulas cadastradas para esta disciplina."
                    : "Você ainda não cadastrou nenhuma aula."
                }}
              </p>
              <Link
                :href="route('lessons.create')"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
              >
                <i class="ri-add-line mr-2"></i>
                Cadastrar Primeira Aula
              </Link>
            </div>

            <div v-else>
              <ul class="divide-y divide-gray-100 dark:divide-gray-700">
                <li
                  v-for="lesson in filteredLessons"
                  :key="lesson.id"
                  class="py-3 sm:py-5 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors duration-150"
                >
                  <div
                    class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
                  >
                    <div class="sm:pr-4 min-w-0 flex-1">
                      <h4
                        class="text-base sm:text-lg font-medium text-gray-900 dark:text-white truncate"
                      >
                        {{ lesson.title }}
                      </h4>
                      <div
                        class="mt-2 flex flex-col space-y-1 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4"
                      >
                        <div
                          class="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400"
                        >
                          <i
                            class="ri-book-open-line flex-shrink-0 mr-1.5 text-indigo-500 dark:text-indigo-400"
                          ></i>
                          <span class="truncate">{{
                            lesson.discipline.name
                          }}</span>
                        </div>
                        <div
                          class="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400"
                        >
                          <i
                            class="ri-calendar-line flex-shrink-0 mr-1.5 text-indigo-500 dark:text-indigo-400"
                          ></i>
                          <span>{{ formatDate(lesson.study_date) }}</span>
                        </div>
                      </div>
                    </div>
                    <div
                      class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2"
                    >
                      <Link
                        :href="route('lessons.show', lesson.id)"
                        class="inline-flex items-center justify-center rounded-md bg-indigo-50 dark:bg-indigo-900/50 px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium text-indigo-700 dark:text-indigo-300 hover:bg-indigo-100 dark:hover:bg-indigo-900/70 transition-colors duration-150"
                      >
                        <i class="ri-eye-line mr-1"></i>
                        Ver Detalhes
                      </Link>
                      <Link
                        :href="route('lessons.edit', lesson.id)"
                        class="inline-flex items-center justify-center rounded-md bg-gray-50 dark:bg-gray-700 px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-150"
                      >
                        <i class="ri-edit-line mr-1"></i>
                        Editar
                      </Link>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  lessons: Array,
  disciplines: Array,
});

const selectedDiscipline = ref("");
const sortBy = ref("date_desc");
const filteredLessons = ref([...props.lessons]);

const filterLessons = () => {
  if (selectedDiscipline.value === "") {
    filteredLessons.value = [...props.lessons];
  } else {
    filteredLessons.value = props.lessons.filter(
      (lesson) => lesson.discipline_id === parseInt(selectedDiscipline.value)
    );
  }

  sortLessons();
};

const sortLessons = () => {
  switch (sortBy.value) {
    case "date_desc":
      filteredLessons.value.sort(
        (a, b) => new Date(b.study_date) - new Date(a.study_date)
      );
      break;
    case "date_asc":
      filteredLessons.value.sort(
        (a, b) => new Date(a.study_date) - new Date(b.study_date)
      );
      break;
    case "title_asc":
      filteredLessons.value.sort((a, b) => a.title.localeCompare(b.title));
      break;
    case "title_desc":
      filteredLessons.value.sort((a, b) => b.title.localeCompare(a.title));
      break;
  }
};

const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

onMounted(() => {
  sortLessons();
});
</script>
