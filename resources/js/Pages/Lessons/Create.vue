<template>
  <Head title="Nova Aula" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="h-6 w-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"
              />
            </svg>
          </div>
          <div>
            <h2
              class="text-xl font-semibold leading-tight text-indigo-800 dark:text-white"
            >
              Nova Aula
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Adicione uma nova aula à sua disciplina
            </p>
          </div>
        </div>
        <Link
          :href="route('lessons.index')"
          class="flex items-center rounded-lg border border-transparent bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 transition-colors hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-950/50 dark:text-indigo-300 dark:hover:bg-indigo-950/70"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="mr-1.5 h-4 w-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"
            />
          </svg>
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-3xl sm:px-6 lg:px-8">
        <div
          class="overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"
                />
              </svg>
              <h3
                class="text-lg font-medium leading-6 text-indigo-700 dark:text-indigo-300"
              >
                Informações da Aula
              </h3>
            </div>
          </div>
          <div class="p-6">
            <form @submit.prevent="submit">
              <div class="space-y-6">
                <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <div
                    class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50 md:col-span-2"
                  >
                    <InputLabel
                      for="title"
                      value="Título da Aula"
                      class="text-gray-700 font-medium dark:text-gray-300"
                    />
                    <TextInput
                      id="title"
                      type="text"
                      class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                      v-model="form.title"
                      placeholder="Ex: Introdução à Termodinâmica, Direito Constitucional..."
                      required
                      autofocus
                    />
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Digite um título claro e descritivo para sua aula
                    </p>
                    <InputError class="mt-2" :message="form.errors.title" />
                  </div>

                  <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                    <InputLabel
                      for="discipline_id"
                      value="Disciplina"
                      class="text-gray-700 font-medium dark:text-gray-300"
                    />
                    <select
                      id="discipline_id"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                      v-model="form.discipline_id"
                      required
                    >
                      <option value="" disabled>
                        Selecione uma disciplina
                      </option>
                      <option
                        v-for="discipline in disciplines"
                        :key="discipline.id"
                        :value="discipline.id"
                      >
                        {{ discipline.name }}
                      </option>
                    </select>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Escolha a disciplina à qual esta aula pertence
                    </p>
                    <InputError
                      class="mt-2"
                      :message="form.errors.discipline_id"
                    />
                  </div>

                  <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                    <InputLabel
                      for="study_date"
                      value="Data de Estudo"
                      class="text-gray-700 font-medium dark:text-gray-300"
                    />
                    <TextInput
                      id="study_date"
                      type="date"
                      class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                      v-model="form.study_date"
                      required
                    />
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Data em que você estudou ou planeja estudar esta aula
                    </p>
                    <InputError
                      class="mt-2"
                      :message="form.errors.study_date"
                    />
                  </div>

                  <div
                    class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50 md:col-span-2"
                  >
                    <InputLabel
                      for="description"
                      value="Descrição (opcional)"
                      class="text-gray-700 font-medium dark:text-gray-300"
                    />
                    <Textarea
                      id="description"
                      class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                      v-model="form.description"
                      placeholder="Descreva brevemente o conteúdo desta aula..."
                      rows="4"
                    />
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Uma breve descrição ajuda a lembrar o conteúdo da aula
                      durante as revisões
                    </p>
                    <InputError
                      class="mt-2"
                      :message="form.errors.description"
                    />
                  </div>
                </div>

                <div
                  class="flex items-center justify-end pt-4 border-t border-gray-200 dark:border-gray-700"
                >
                  <Link
                    :href="route('lessons.index')"
                    class="rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:text-gray-300 dark:hover:bg-gray-800"
                  >
                    Cancelar
                  </Link>
                  <PrimaryButton
                    class="ml-4 bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                    :class="{ 'opacity-25': form.processing }"
                    :disabled="form.processing"
                  >
                    <svg
                      v-if="form.processing"
                      class="mr-2 h-4 w-4 animate-spin"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>{{
                      form.processing ? "Salvando..." : "Salvar Aula"
                    }}</span>
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { onMounted } from "vue";
import { Head, Link, useForm } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import Textarea from "@/Components/Textarea.vue";

const props = defineProps({
  disciplines: Array,
  disciplineId: {
    type: [String, Number],
    default: "",
  },
});

const form = useForm({
  title: "",
  description: "",
  study_date: new Date().toISOString().split("T")[0], // Today's date as default
  discipline_id: props.disciplineId || "",
});

const submit = () => {
  form.post(route("lessons.store"));
};

onMounted(() => {
  // If there's only one discipline, select it automatically
  if (props.disciplines.length === 1 && !form.discipline_id) {
    form.discipline_id = props.disciplines[0].id;
  }
});
</script>
