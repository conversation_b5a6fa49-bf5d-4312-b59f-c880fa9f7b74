<template>
  <Head title="Editar Mapa Mental" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Editar Mapa Mental para "{{ mindMap.lesson.title }}"
        </h2>
        <Link
          :href="route('lessons.show', mindMap.lesson.id)"
          class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
        >
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="mb-6">
              <p class="text-sm text-gray-600">
                Edite o mapa mental para organizar os conceitos da aula. Você
                pode adicionar nós, conectá-los e editá-los.
              </p>
            </div>

            <MindMapEditor
              :initial-data="mindMapData"
              :lesson-id="mindMap.lesson.id"
              :mind-map-id="mindMap.id"
              @save="saveMindMap"
            />
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import MindMapEditor from "@/Components/MindMapEditor.vue";

const props = defineProps({
  mindMap: Object,
});

const mindMapData = ref({
  content: "",
});

// Inicializar os dados do mapa mental imediatamente, sem esperar pelo onMounted
// Isso garante que os dados estejam disponíveis quando o componente MindMapEditor for montado
if (props.mindMap.content) {
  mindMapData.value = {
    content: props.mindMap.content,
  };
} else {
  // Conteúdo padrão se não houver nada
  mindMapData.value = {
    content: `# ${props.mindMap.lesson.title}\n## Subtópico 1\n## Subtópico 2`,
  };
}

const saveMindMap = (mapData) => {
  router.put(
    route("mind-maps.update", props.mindMap.id),
    {
      content: mapData.content,
    },
    {
      onSuccess: () => {
        router.visit(route("lessons.show", props.mindMap.lesson.id));
      },
    }
  );
};
</script>
