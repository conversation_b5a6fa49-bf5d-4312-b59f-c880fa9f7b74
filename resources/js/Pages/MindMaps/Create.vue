<template>
  <Head title="Criar Mapa Mental" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Criar Mapa Mental para "{{ lesson.title }}"
        </h2>
        <Link
          :href="route('lessons.show', lesson.id)"
          class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
        >
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="mb-6">
              <p class="text-sm text-gray-600">
                Crie um mapa mental para organizar os conceitos da aula. Você
                pode adicionar nós, conectá-los e editá-los.
              </p>
            </div>

            <MindMapEditor
              :initial-data="initialData"
              :lesson-id="lesson.id"
              @save="saveMindMap"
            />
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import MindMapEditor from "@/Components/MindMapEditor.vue";

const props = defineProps({
  lesson: Object,
});

const initialData = ref({
  content: `# ${props.lesson.title}\n## Subtópico 1\n## Subtópico 2`,
});

const saveMindMap = (mapData) => {
  router.post(
    route("mind-maps.store"),
    {
      content: mapData.content,
      lesson_id: props.lesson.id,
    },
    {
      onSuccess: () => {
        router.visit(route("lessons.show", props.lesson.id));
      },
    }
  );
};
</script>
