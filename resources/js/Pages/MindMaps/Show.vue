<template>
  <Head title="Visualizar Mapa Mental" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Mapa Mental: {{ mindMap.lesson.title }}
        </h2>
        <div class="flex space-x-2">
          <Link
            :href="route('lessons.show', mindMap.lesson.id)"
            class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
          >
            Voltar
          </Link>
          <Link
            :href="route('mind-maps.edit', mindMap.id)"
            class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          >
            Editar
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="flex justify-center">
              <div class="w-full max-w-4xl">
                <MindMapPreview :data="mindMapData" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import MindMapPreview from "@/Components/MindMapPreview.vue";

const props = defineProps({
  mindMap: Object,
});

const mindMapData = ref({
  content: "",
});

onMounted(() => {
  // Agora o conteúdo já está em formato de texto (Markdown)
  if (props.mindMap.content) {
    mindMapData.value = {
      content: props.mindMap.content,
    };
  } else {
    // Conteúdo padrão se não houver nada
    mindMapData.value = {
      content: `# ${props.mindMap.lesson.title}\n## Sem conteúdo disponível`,
    };
  }
});
</script>
