<template>
  <Head title="Editar Disciplina" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
          >
            <i class="ri-edit-line text-2xl"></i>
          </div>
          <div>
            <h2
              class="text-xl font-semibold leading-tight text-indigo-800 dark:text-white"
            >
              Editar Disciplina
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ discipline.name }}
            </p>
          </div>
        </div>
        <Link
          :href="route('disciplines.show', discipline.id)"
          class="flex items-center rounded-lg border border-transparent bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 transition-colors hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-950/50 dark:text-indigo-300 dark:hover:bg-indigo-950/70"
        >
          <i class="ri-arrow-left-line mr-1.5"></i>
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-3xl sm:px-6 lg:px-8">
        <div
          class="overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <div class="flex items-center">
              <i
                class="ri-graduation-cap-line mr-2 text-indigo-600 dark:text-indigo-400"
              ></i>
              <h3
                class="text-lg font-medium leading-6 text-indigo-700 dark:text-indigo-300"
              >
                Informações da Disciplina
              </h3>
            </div>
          </div>
          <div class="p-6">
            <form @submit.prevent="submit">
              <div class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                  <InputLabel
                    for="name"
                    value="Nome da Disciplina"
                    class="text-gray-700 font-medium dark:text-gray-300"
                  />
                  <TextInput
                    id="name"
                    type="text"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    v-model="form.name"
                    placeholder="Ex: Matemática, Física, Direito Civil..."
                    required
                    autofocus
                  />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Digite o nome completo da disciplina que você está estudando
                  </p>
                  <InputError class="mt-2" :message="form.errors.name" />
                </div>

                <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                  <InputLabel
                    for="description"
                    value="Descrição (opcional)"
                    class="text-gray-700 font-medium dark:text-gray-300"
                  />
                  <Textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    v-model="form.description"
                    placeholder="Descreva brevemente o conteúdo ou objetivo desta disciplina..."
                    rows="4"
                  />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Uma breve descrição ajuda a organizar melhor seus estudos
                  </p>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                  <InputLabel
                    for="discipline_group_id"
                    value="Grupo (opcional)"
                    class="text-gray-700 font-medium dark:text-gray-300"
                  />
                  <select
                    id="discipline_group_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    v-model="form.discipline_group_id"
                  >
                    <option value="">Selecione um grupo</option>
                    <option
                      v-for="group in disciplineGroups"
                      :key="group.id"
                      :value="group.id"
                    >
                      {{ group.name }} {{ group.is_default ? "(Padrão)" : "" }}
                    </option>
                  </select>
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Agrupar disciplinas ajuda a organizar melhor seus estudos
                  </p>
                  <InputError
                    class="mt-2"
                    :message="form.errors.discipline_group_id"
                  />
                </div>

                <div
                  class="flex items-center justify-end pt-4 border-t border-gray-200 dark:border-gray-700"
                >
                  <Link
                    :href="route('disciplines.show', discipline.id)"
                    class="rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:text-gray-300 dark:hover:bg-gray-800"
                  >
                    Cancelar
                  </Link>
                  <PrimaryButton
                    class="ml-4 bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                    :class="{ 'opacity-25': form.processing }"
                    :disabled="form.processing"
                  >
                    <i
                      v-if="form.processing"
                      class="ri-loader-4-line mr-2 animate-spin"
                    ></i>
                    <span>{{
                      form.processing ? "Salvando..." : "Atualizar Disciplina"
                    }}</span>
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import Textarea from "@/Components/Textarea.vue";

const props = defineProps({
  discipline: Object,
  disciplineGroups: Array,
});

const form = useForm({
  name: props.discipline.name,
  description: props.discipline.description || "",
  discipline_group_id: props.discipline.discipline_group_id || "",
});

const submit = () => {
  form.put(route("disciplines.update", props.discipline.id));
};
</script>
