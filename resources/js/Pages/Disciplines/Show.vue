<template>
  <Head :title="discipline.name" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          {{ discipline.name }}
        </h2>
        <div class="flex space-x-2">
          <Link
            :href="route('disciplines.index')"
            class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
          >
            Voltar
          </Link>
          <Link
            :href="route('disciplines.edit', discipline.id)"
            class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          >
            Editar
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Discipline Info -->
        <div
          class="mb-8 overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
        >
          <div class="relative">
            <div
              class="absolute inset-0 h-32 bg-gradient-to-r from-indigo-600 to-indigo-800"
            ></div>
            <div class="relative px-4 py-5 sm:px-6 pt-16 pb-4">
              <div
                class="bg-white rounded-lg shadow-md p-6 border border-gray-100"
              >
                <div class="flex items-center justify-between mb-4">
                  <h3
                    class="text-xl font-semibold text-gray-900 flex items-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6 mr-2 text-indigo-600"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      />
                    </svg>
                    Informações da Disciplina
                  </h3>
                </div>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 mr-1 text-indigo-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      Nome
                    </dt>
                    <dd class="mt-1 text-base text-gray-900 font-medium">
                      {{ discipline.name }}
                    </dd>
                  </div>
                  <div class="sm:col-span-2">
                    <dt
                      class="text-sm font-medium text-gray-500 flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 mr-1 text-indigo-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M4 6h16M4 12h16M4 18h7"
                        />
                      </svg>
                      Descrição
                    </dt>
                    <dd
                      class="mt-1 text-sm text-gray-700 bg-gray-50 p-3 rounded-md"
                    >
                      {{ discipline.description || "Sem descrição" }}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- Lessons -->
        <div
          class="overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
        >
          <div class="border-b border-gray-100 bg-white px-4 py-5 sm:px-6">
            <div class="flex items-center justify-between">
              <h3
                class="text-lg font-medium leading-6 text-gray-900 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-indigo-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                Aulas
              </h3>
              <Link
                :href="
                  route('lessons.create', { discipline_id: discipline.id })
                "
                class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition duration-150 ease-in-out flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                Nova Aula
              </Link>
            </div>
          </div>
          <div class="px-4 py-5 sm:p-6">
            <div v-if="lessons.length === 0" class="text-center py-8">
              <div class="mx-auto h-24 w-24 text-indigo-300 mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Nenhuma aula cadastrada
              </h3>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">
                Cadastre sua primeira aula para esta disciplina e comece a
                organizar seu conteúdo de estudo.
              </p>
              <Link
                :href="
                  route('lessons.create', { discipline_id: discipline.id })
                "
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                Cadastrar Primeira Aula
              </Link>
            </div>

            <div v-else>
              <ul class="divide-y divide-gray-100">
                <li
                  v-for="lesson in lessons"
                  :key="lesson.id"
                  class="py-5 hover:bg-gray-50 rounded-lg transition-colors duration-150"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0 pr-4">
                      <h4 class="text-lg font-medium text-gray-900 truncate">
                        {{ lesson.title }}
                      </h4>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="flex-shrink-0 mr-1.5 h-4 w-4 text-indigo-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                        <span
                          >Data de estudo:
                          {{ formatDate(lesson.study_date) }}</span
                        >
                      </div>
                    </div>
                    <div class="flex-shrink-0">
                      <Link
                        :href="route('lessons.show', lesson.id)"
                        class="inline-flex items-center rounded-md bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-150"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-4 w-4 mr-1"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path
                            fill-rule="evenodd"
                            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                        Ver Detalhes
                      </Link>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  discipline: Object,
  lessons: Array,
});

const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};
</script>
