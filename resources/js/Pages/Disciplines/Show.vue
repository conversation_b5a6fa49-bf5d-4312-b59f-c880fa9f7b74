<template>
  <Head :title="discipline.name" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          {{ discipline.name }}
        </h2>
        <div class="flex space-x-2">
          <Link
            :href="route('disciplines.index')"
            class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
          >
            Voltar
          </Link>
          <Link
            :href="route('disciplines.edit', discipline.id)"
            class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          >
            Editar
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Discipline Info -->
        <div
          class="mb-8 overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
        >
          <div class="relative">
            <div
              class="absolute inset-0 h-32 bg-gradient-to-r from-indigo-600 to-indigo-800"
            ></div>
            <div class="relative px-4 py-5 sm:px-6 pt-16 pb-4">
              <div
                class="bg-white rounded-lg shadow-md p-6 border border-gray-100"
              >
                <div class="flex items-center justify-between mb-4">
                  <h3
                    class="text-xl font-semibold text-gray-900 flex items-center"
                  >
                    <i
                      class="ri-book-open-line mr-2 text-indigo-600 text-2xl"
                    ></i>
                    Informações da Disciplina
                  </h3>
                </div>
                <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                  <div class="sm:col-span-1">
                    <dt
                      class="text-sm font-medium text-gray-500 flex items-center"
                    >
                      <i class="ri-information-line mr-1 text-indigo-500"></i>
                      Nome
                    </dt>
                    <dd class="mt-1 text-base text-gray-900 font-medium">
                      {{ discipline.name }}
                    </dd>
                  </div>
                  <div class="sm:col-span-2">
                    <dt
                      class="text-sm font-medium text-gray-500 flex items-center"
                    >
                      <i class="ri-file-text-line mr-1 text-indigo-500"></i>
                      Descrição
                    </dt>
                    <dd
                      class="mt-1 text-sm text-gray-700 bg-gray-50 p-3 rounded-md"
                    >
                      {{ discipline.description || "Sem descrição" }}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <!-- Lessons -->
        <div
          class="overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
        >
          <div class="border-b border-gray-100 bg-white px-4 py-5 sm:px-6">
            <div class="flex items-center justify-between">
              <h3
                class="text-lg font-medium leading-6 text-gray-900 flex items-center"
              >
                <i class="ri-file-list-3-line mr-2 text-indigo-600 text-xl"></i>
                Aulas
              </h3>
              <Link
                :href="
                  route('lessons.create', { discipline_id: discipline.id })
                "
                class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 transition duration-150 ease-in-out flex items-center"
              >
                <i class="ri-add-line mr-1"></i>
                Nova Aula
              </Link>
            </div>
          </div>
          <div class="px-4 py-5 sm:p-6">
            <div v-if="lessons.length === 0" class="text-center py-8">
              <div class="mx-auto h-24 w-24 text-indigo-300 mb-4">
                <i class="ri-file-list-3-line text-8xl"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Nenhuma aula cadastrada
              </h3>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">
                Cadastre sua primeira aula para esta disciplina e comece a
                organizar seu conteúdo de estudo.
              </p>
              <Link
                :href="
                  route('lessons.create', { discipline_id: discipline.id })
                "
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
              >
                <i class="ri-add-line mr-2 text-xl"></i>
                Cadastrar Primeira Aula
              </Link>
            </div>

            <div v-else>
              <ul class="divide-y divide-gray-100">
                <li
                  v-for="lesson in lessons"
                  :key="lesson.id"
                  class="py-5 hover:bg-gray-50 rounded-lg transition-colors duration-150"
                >
                  <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0 pr-4">
                      <h4 class="text-lg font-medium text-gray-900 truncate">
                        {{ lesson.title }}
                      </h4>
                      <div class="mt-1 flex items-center text-sm text-gray-500">
                        <i
                          class="ri-calendar-line flex-shrink-0 mr-1.5 text-indigo-500"
                        ></i>
                        <span
                          >Data de estudo:
                          {{ formatDate(lesson.study_date) }}</span
                        >
                      </div>
                    </div>
                    <div class="flex-shrink-0">
                      <Link
                        :href="route('lessons.show', lesson.id)"
                        class="inline-flex items-center rounded-md bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-colors duration-150"
                      >
                        <i class="ri-eye-line mr-1"></i>
                        Ver Detalhes
                      </Link>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  discipline: Object,
  lessons: Array,
});

const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};
</script>
