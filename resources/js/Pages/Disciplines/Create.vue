<template>
  <Head title="Nova Disciplina" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke-width="1.5"
              stroke="currentColor"
              class="h-6 w-6"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M12 4.5v15m7.5-7.5h-15"
              />
            </svg>
          </div>
          <div>
            <h2
              class="text-xl font-semibold leading-tight text-indigo-800 dark:text-white"
            >
              Nova Disciplina
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Adicione uma nova disciplina ao seu plano de estudos
            </p>
          </div>
        </div>
        <Link
          :href="route('disciplines.index')"
          class="flex items-center rounded-lg border border-transparent bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 transition-colors hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-950/50 dark:text-indigo-300 dark:hover:bg-indigo-950/70"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="mr-1.5 h-4 w-4"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"
            />
          </svg>
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-3xl sm:px-6 lg:px-8">
        <div
          class="overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <div class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="mr-2 h-5 w-5 text-indigo-600 dark:text-indigo-400"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-********* 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443m-7.007 11.55A5.981 5.981 0 006.75 15.75v-1.5"
                />
              </svg>
              <h3
                class="text-lg font-medium leading-6 text-indigo-700 dark:text-indigo-300"
              >
                Informações da Disciplina
              </h3>
            </div>
          </div>
          <div class="p-6">
            <form @submit.prevent="submit">
              <div class="space-y-6">
                <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                  <InputLabel
                    for="name"
                    value="Nome da Disciplina"
                    class="text-gray-700 font-medium dark:text-gray-300"
                  />
                  <TextInput
                    id="name"
                    type="text"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    v-model="form.name"
                    placeholder="Ex: Matemática, Física, Direito Civil..."
                    required
                    autofocus
                  />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Digite o nome completo da disciplina que você está estudando
                  </p>
                  <InputError class="mt-2" :message="form.errors.name" />
                </div>

                <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                  <InputLabel
                    for="description"
                    value="Descrição (opcional)"
                    class="text-gray-700 font-medium dark:text-gray-300"
                  />
                  <Textarea
                    id="description"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    v-model="form.description"
                    placeholder="Descreva brevemente o conteúdo ou objetivo desta disciplina..."
                    rows="4"
                  />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Uma breve descrição ajuda a organizar melhor seus estudos
                  </p>
                  <InputError class="mt-2" :message="form.errors.description" />
                </div>

                <div class="bg-gray-50 p-4 rounded-lg dark:bg-zinc-800/50">
                  <InputLabel
                    for="discipline_group_id"
                    value="Grupo (opcional)"
                    class="text-gray-700 font-medium dark:text-gray-300"
                  />
                  <select
                    id="discipline_group_id"
                    class="mt-1 block w-full border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 rounded-md shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                    v-model="form.discipline_group_id"
                  >
                    <option value="">Selecione um grupo</option>
                    <option
                      v-for="group in disciplineGroups"
                      :key="group.id"
                      :value="group.id"
                    >
                      {{ group.name }} {{ group.is_default ? "(Padrão)" : "" }}
                    </option>
                  </select>
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Agrupar disciplinas ajuda a organizar melhor seus estudos
                  </p>
                  <InputError
                    class="mt-2"
                    :message="form.errors.discipline_group_id"
                  />
                </div>

                <div class="flex items-center justify-end pt-4">
                  <Link
                    :href="route('disciplines.index')"
                    class="rounded-lg px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:text-gray-300 dark:hover:bg-gray-800"
                  >
                    Cancelar
                  </Link>
                  <PrimaryButton
                    class="ml-4 bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                    :class="{ 'opacity-25': form.processing }"
                    :disabled="form.processing"
                  >
                    <svg
                      v-if="form.processing"
                      class="mr-2 h-4 w-4 animate-spin"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span>{{
                      form.processing ? "Salvando..." : "Salvar Disciplina"
                    }}</span>
                  </PrimaryButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import Textarea from "@/Components/Textarea.vue";

const props = defineProps({
  disciplineGroups: Array,
});

const form = useForm({
  name: "",
  description: "",
  discipline_group_id: "",
});

const submit = () => {
  form.post(route("disciplines.store"));
};
</script>
