<template>
  <Head title="Disciplinas" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Disciplinas
        </h2>
        <Link
          :href="route('disciplines.create')"
          class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          Nova Disciplina
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Banner informativo -->
        <div
          class="mb-8 bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-lg shadow-md overflow-hidden"
        >
          <div
            class="px-6 py-5 sm:px-8 sm:py-6 flex flex-col md:flex-row items-center justify-between"
          >
            <div class="flex items-center mb-4 md:mb-0">
              <div class="flex-shrink-0 bg-white/20 rounded-full p-2 mr-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-8 w-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-white">
                  Organize seus estudos
                </h3>
                <p class="text-indigo-100 text-sm">
                  Crie disciplinas para organizar suas aulas e revisões
                </p>
              </div>
            </div>
            <Link
              :href="route('disciplines.create')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white transition-colors duration-150"
            >
              Começar agora
            </Link>
          </div>
        </div>

        <!-- Campo de busca -->
        <div class="mb-7 flex items-center justify-between">
          <div class="relative w-full max-w-md">
            <div
              class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
            >
              <i class="ri-search-line text-gray-400"></i>
            </div>
            <input
              type="text"
              v-model="searchQuery"
              class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 p-2.5"
              placeholder="Buscar disciplina por nome..."
              @input="filterDisciplines"
            />
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 hover:text-gray-700"
            >
              <i class="ri-close-line"></i>
            </button>
          </div>
          <div class="ml-4 text-sm text-gray-500">
            {{ filteredDisciplines.length }}
            {{
              filteredDisciplines.length === 1
                ? "disciplina encontrada"
                : "disciplinas encontradas"
            }}
          </div>
        </div>
        <div
          v-if="
            ungroupedDisciplines.length === 0 && disciplineGroups.length === 0
          "
          class="bg-white rounded-lg shadow-md p-8 text-center"
        >
          <div class="mx-auto h-24 w-24 text-indigo-400 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Você ainda não cadastrou nenhuma disciplina
          </h3>
          <p class="text-gray-500 mb-6 max-w-md mx-auto">
            Crie sua primeira disciplina para começar a organizar seus estudos e
            acompanhar seu progresso.
          </p>
          <Link
            :href="route('disciplines.create')"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-150"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clip-rule="evenodd"
              />
            </svg>
            Cadastrar Primeira Disciplina
          </Link>
        </div>

        <!-- Mensagem quando não há resultados na busca -->
        <div
          v-else-if="searchQuery && filteredDisciplines.length === 0"
          class="bg-white rounded-lg shadow-md p-8 text-center"
        >
          <div class="mx-auto h-16 w-16 text-indigo-400 mb-4">
            <i class="ri-search-line text-5xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            Nenhuma disciplina encontrada
          </h3>
          <p class="text-gray-500 mb-6 max-w-md mx-auto">
            Não encontramos nenhuma disciplina com o nome "<span
              class="font-medium"
              >{{ searchQuery }}</span
            >". Tente outro termo ou
            <button
              @click="clearSearch"
              class="text-indigo-600 hover:text-indigo-800 font-medium"
            >
              limpe a busca</button
            >.
          </p>
        </div>

        <div v-else>
          <!-- Grupos de disciplinas -->
          <div
            v-for="group in disciplineGroups"
            :key="'group-' + group.id"
            class="mb-8"
          >
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div
                  class="flex h-8 w-8 items-center justify-center rounded-md bg-indigo-100 text-indigo-600 mr-2"
                >
                  <i class="ri-folder-line"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800">
                  {{ group.name }}
                  <span
                    v-if="group.is_default"
                    class="ml-2 rounded-full bg-indigo-100 px-2 py-0.5 text-xs font-medium text-indigo-800"
                  >
                    Padrão
                  </span>
                </h3>
              </div>
              <Link
                :href="route('discipline-groups.show', group.id)"
                class="text-sm text-indigo-600 hover:text-indigo-800 flex items-center"
              >
                Ver grupo <i class="ri-arrow-right-line ml-1"></i>
              </Link>
            </div>

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div
                v-for="discipline in filterDisciplinesByGroup(group.id)"
                :key="discipline.id"
                class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg hover:border-indigo-200 flex flex-col h-[280px]"
              >
                <!-- Cabeçalho do card com gradiente -->
                <div
                  class="h-2 bg-gradient-to-r from-indigo-500 to-indigo-700"
                ></div>

                <!-- Título da disciplina -->
                <div class="border-b border-gray-100 bg-white px-5 py-4">
                  <h3
                    class="text-lg font-medium leading-6 text-gray-900 truncate"
                    :title="discipline.name"
                  >
                    {{ discipline.name }}
                  </h3>
                </div>

                <!-- Conteúdo do card -->
                <div class="px-5 py-4 flex-grow flex flex-col">
                  <!-- Descrição com altura fixa e truncamento -->
                  <div class="mb-3 flex-grow">
                    <p
                      class="text-sm text-gray-600 line-clamp-3 h-[60px]"
                      v-if="discipline.description"
                      :title="discipline.description"
                    >
                      {{ discipline.description }}
                    </p>
                    <p class="text-sm text-gray-500 italic h-[60px]" v-else>
                      Sem descrição
                    </p>
                  </div>

                  <!-- Estatísticas -->
                  <div
                    class="flex items-center mb-4 text-xs text-gray-500 space-x-4"
                  >
                    <div class="flex items-center">
                      <i class="ri-book-open-line mr-1 text-indigo-500"></i>
                      <span
                        >{{
                          discipline.lessons ? discipline.lessons.length : 0
                        }}
                        aulas</span
                      >
                    </div>
                    <div class="flex items-center">
                      <i class="ri-time-line mr-1 text-indigo-500"></i>
                      <span>{{ formatDate(discipline.created_at) }}</span>
                    </div>
                  </div>

                  <!-- Botões de ação -->
                  <div
                    class="flex justify-between pt-3 border-t border-gray-100"
                  >
                    <!-- Botão principal -->
                    <Link
                      :href="route('disciplines.show', discipline.id)"
                      class="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition-colors duration-150 w-[120px]"
                    >
                      <i class="ri-eye-line mr-1"></i>
                      Detalhes
                    </Link>

                    <!-- Botões secundários -->
                    <div class="flex space-x-2">
                      <Link
                        :href="route('disciplines.edit', discipline.id)"
                        class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors duration-150"
                        title="Editar disciplina"
                      >
                        <i class="ri-edit-line"></i>
                      </Link>
                      <button
                        @click="confirmDelete(discipline)"
                        class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-red-600 text-sm font-medium rounded-md hover:bg-red-50 transition-colors duration-150"
                        title="Excluir disciplina"
                      >
                        <i class="ri-delete-bin-line"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Disciplinas sem grupo -->
          <div v-if="filteredUngroupedDisciplines.length > 0" class="mt-8">
            <div class="flex items-center mb-4">
              <div
                class="flex h-8 w-8 items-center justify-center rounded-md bg-gray-100 text-gray-600 mr-2"
              >
                <i class="ri-file-list-line"></i>
              </div>
              <h3 class="text-lg font-semibold text-gray-800">
                Disciplinas sem grupo
              </h3>
            </div>

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              <div
                v-for="discipline in filteredUngroupedDisciplines"
                :key="discipline.id"
                class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-md transition-all duration-300 hover:shadow-lg hover:border-indigo-200 flex flex-col h-[280px]"
              >
                <!-- Cabeçalho do card com gradiente -->
                <div
                  class="h-2 bg-gradient-to-r from-indigo-500 to-indigo-700"
                ></div>

                <!-- Título da disciplina -->
                <div class="border-b border-gray-100 bg-white px-5 py-4">
                  <h3
                    class="text-lg font-medium leading-6 text-gray-900 truncate"
                    :title="discipline.name"
                  >
                    {{ discipline.name }}
                  </h3>
                </div>

                <!-- Conteúdo do card -->
                <div class="px-5 py-4 flex-grow flex flex-col">
                  <!-- Descrição com altura fixa e truncamento -->
                  <div class="mb-3 flex-grow">
                    <p
                      class="text-sm text-gray-600 line-clamp-3 h-[60px]"
                      v-if="discipline.description"
                      :title="discipline.description"
                    >
                      {{ discipline.description }}
                    </p>
                    <p class="text-sm text-gray-500 italic h-[60px]" v-else>
                      Sem descrição
                    </p>
                  </div>

                  <!-- Estatísticas -->
                  <div
                    class="flex items-center mb-4 text-xs text-gray-500 space-x-4"
                  >
                    <div class="flex items-center">
                      <i class="ri-book-open-line mr-1 text-indigo-500"></i>
                      <span
                        >{{
                          discipline.lessons ? discipline.lessons.length : 0
                        }}
                        aulas</span
                      >
                    </div>
                    <div class="flex items-center">
                      <i class="ri-time-line mr-1 text-indigo-500"></i>
                      <span>{{ formatDate(discipline.created_at) }}</span>
                    </div>
                  </div>

                  <!-- Botões de ação -->
                  <div
                    class="flex justify-between pt-3 border-t border-gray-100"
                  >
                    <!-- Botão principal -->
                    <Link
                      :href="route('disciplines.show', discipline.id)"
                      class="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition-colors duration-150 w-[120px]"
                    >
                      <i class="ri-eye-line mr-1"></i>
                      Detalhes
                    </Link>

                    <!-- Botões secundários -->
                    <div class="flex space-x-2">
                      <Link
                        :href="route('disciplines.edit', discipline.id)"
                        class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors duration-150"
                        title="Editar disciplina"
                      >
                        <i class="ri-edit-line"></i>
                      </Link>
                      <button
                        @click="confirmDelete(discipline)"
                        class="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-red-600 text-sm font-medium rounded-md hover:bg-red-50 transition-colors duration-150"
                        title="Excluir disciplina"
                      >
                        <i class="ri-delete-bin-line"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <Modal :show="showDeleteModal" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">Confirmar Exclusão</h2>
        <p class="mt-1 text-sm text-gray-600">
          Tem certeza que deseja excluir a disciplina "{{
            disciplineToDelete?.name
          }}"?
        </p>
        <div class="mt-2 rounded-md bg-yellow-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="ri-alert-line text-yellow-400 text-lg"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">Atenção</h3>
              <div class="mt-2 text-sm text-yellow-700">
                <p>Esta ação excluirá permanentemente:</p>
                <ul class="list-disc pl-5 space-y-1 mt-1">
                  <li>Todas as aulas desta disciplina</li>
                  <li>Todos os mapas mentais associados</li>
                  <li>Todas as gravações de áudio</li>
                  <li>Todas as revisões programadas</li>
                </ul>
                <p class="mt-2 font-semibold">
                  Esta ação não pode ser desfeita.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeModal">Cancelar</SecondaryButton>
          <DangerButton
            @click="deleteDiscipline"
            :class="{ 'opacity-25': processing }"
            :disabled="processing"
          >
            Excluir Disciplina
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import DangerButton from "@/Components/DangerButton.vue";

const props = defineProps({
  disciplineGroups: Array,
  ungroupedDisciplines: Array,
});

const showDeleteModal = ref(false);
const disciplineToDelete = ref(null);
const processing = ref(false);
const searchQuery = ref("");

// Combine all disciplines for search functionality
const allDisciplines = computed(() => {
  const grouped = props.disciplineGroups.flatMap((group) =>
    group.disciplines.map((discipline) => ({
      ...discipline,
      group_name: group.name,
      group_id: group.id,
    }))
  );

  const ungrouped = props.ungroupedDisciplines.map((discipline) => ({
    ...discipline,
    group_name: null,
    group_id: null,
  }));

  return [...grouped, ...ungrouped];
});

const filteredDisciplines = ref([...allDisciplines.value]);

// Função para formatar a data
const formatDate = (dateString) => {
  if (!dateString) return "Data desconhecida";

  const options = { day: "2-digit", month: "2-digit", year: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

// Computed property for filtered disciplines by group
const filteredDisciplinesByGroup = computed(() => {
  const result = {};

  props.disciplineGroups.forEach((group) => {
    if (!searchQuery.value.trim()) {
      result[group.id] = group.disciplines;
    } else {
      const query = searchQuery.value.toLowerCase().trim();
      result[group.id] = group.disciplines.filter((discipline) =>
        discipline.name.toLowerCase().includes(query)
      );
    }
  });

  return result;
});

// Computed property for filtered ungrouped disciplines
const filteredUngroupedDisciplines = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.ungroupedDisciplines;
  }

  const query = searchQuery.value.toLowerCase().trim();
  return props.ungroupedDisciplines.filter((discipline) =>
    discipline.name.toLowerCase().includes(query)
  );
});

// Helper function to get disciplines for a specific group
const filterDisciplinesByGroup = (groupId) => {
  return filteredDisciplinesByGroup.value[groupId] || [];
};

// Função para filtrar disciplinas com base na busca
const filterDisciplines = () => {
  if (!searchQuery.value.trim()) {
    filteredDisciplines.value = [...allDisciplines.value];
    return;
  }

  const query = searchQuery.value.toLowerCase().trim();
  filteredDisciplines.value = allDisciplines.value.filter((discipline) =>
    discipline.name.toLowerCase().includes(query)
  );
};

// Função para limpar a busca
const clearSearch = () => {
  searchQuery.value = "";
  filterDisciplines();
};

const confirmDelete = (discipline) => {
  disciplineToDelete.value = discipline;
  showDeleteModal.value = true;
};

const closeModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    disciplineToDelete.value = null;
  }, 300);
};

const deleteDiscipline = () => {
  if (!disciplineToDelete.value) return;

  processing.value = true;

  router.delete(route("disciplines.destroy", disciplineToDelete.value.id), {
    onSuccess: () => {
      // Remover a disciplina excluída da lista filtrada e da lista original
      const deletedId = disciplineToDelete.value.id;
      filteredDisciplines.value = filteredDisciplines.value.filter(
        (discipline) => discipline.id !== deletedId
      );

      // Fechar o modal e redefinir o estado
      closeModal();
      processing.value = false;

      // Recarregar a página para garantir que todos os dados estejam atualizados
      router.reload({ only: ["disciplines"] });
    },
    onError: () => {
      processing.value = false;
    },
  });
};
</script>
