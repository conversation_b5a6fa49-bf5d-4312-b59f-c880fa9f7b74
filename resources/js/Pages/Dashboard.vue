<script setup>
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import { Head, <PERSON> } from "@inertiajs/vue3";
</script>

<template>
  <Head title="Dashboard" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="text-xl font-semibold leading-tight text-gray-800">
        Bem-vindo ao Aprovado
      </h2>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Welcome Banner -->
        <div
          class="mb-8 overflow-hidden rounded-lg bg-gradient-to-r from-indigo-600 to-indigo-800 shadow-lg"
        >
          <div class="px-8 py-10 sm:px-10">
            <div class="flex flex-col md:flex-row items-center">
              <div class="mb-6 md:mb-0 md:mr-8 flex-shrink-0">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-24 w-24 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                  />
                </svg>
              </div>
              <div>
                <h3 class="text-2xl font-bold text-white mb-2">
                  Sistema de Revisão Espaçada
                </h3>
                <p class="text-indigo-100 mb-4">
                  Maximize sua retenção de conhecimento com nosso sistema de
                  revisão espaçada cientificamente comprovado.
                </p>
                <div class="flex flex-wrap gap-3">
                  <Link
                    :href="route('lessons.create')"
                    class="inline-flex items-center rounded-md bg-white px-4 py-2 text-sm font-medium text-indigo-700 shadow-sm hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600"
                  >
                    <svg
                      class="-ml-1 mr-2 h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    Nova Aula
                  </Link>
                  <Link
                    :href="route('revisions.index')"
                    class="inline-flex items-center rounded-md border border-transparent border-white px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-indigo-600"
                  >
                    Ver Revisões
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="mb-8 grid grid-cols-1 gap-6 md:grid-cols-4">
          <Link
            :href="route('disciplines.index')"
            class="flex flex-col items-center justify-center rounded-lg bg-white p-8 text-center shadow-md transition hover:shadow-lg border border-gray-100"
          >
            <div class="mb-4 rounded-full bg-indigo-100 p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-10 w-10 text-indigo-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                />
              </svg>
            </div>
            <h3 class="mb-2 text-lg font-semibold text-gray-900">
              Disciplinas
            </h3>
            <p class="text-sm text-gray-600">
              Organize seus estudos por disciplinas
            </p>
          </Link>

          <Link
            :href="route('lessons.index')"
            class="flex flex-col items-center justify-center rounded-lg bg-white p-8 text-center shadow-md transition hover:shadow-lg border border-gray-100"
          >
            <div class="mb-4 rounded-full bg-indigo-100 p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-10 w-10 text-indigo-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
            </div>
            <h3 class="mb-2 text-lg font-semibold text-gray-900">Aulas</h3>
            <p class="text-sm text-gray-600">Cadastre e gerencie suas aulas</p>
          </Link>

          <Link
            :href="route('study-sessions.index')"
            class="flex flex-col items-center justify-center rounded-lg bg-white p-8 text-center shadow-md transition hover:shadow-lg border border-gray-100"
          >
            <div class="mb-4 rounded-full bg-indigo-100 p-3">
              <i class="ri-timer-line text-indigo-600 text-3xl"></i>
            </div>
            <h3 class="mb-2 text-lg font-semibold text-gray-900">
              Sessões de Estudo
            </h3>
            <p class="text-sm text-gray-600">Registre seu tempo de estudo</p>
          </Link>

          <Link
            :href="route('calendar.index')"
            class="flex flex-col items-center justify-center rounded-lg bg-white p-8 text-center shadow-md transition hover:shadow-lg border border-gray-100"
          >
            <div class="mb-4 rounded-full bg-indigo-100 p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-10 w-10 text-indigo-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h3 class="mb-2 text-lg font-semibold text-gray-900">Calendário</h3>
            <p class="text-sm text-gray-600">
              Visualize suas revisões no calendário
            </p>
          </Link>
        </div>

        <!-- System Info -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div
            class="md:col-span-2 overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
          >
            <div class="p-6">
              <h3
                class="mb-4 text-lg font-medium text-gray-900 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-indigo-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Sobre o Sistema de Revisões
              </h3>
              <div class="prose max-w-none">
                <p>
                  O sistema
                  <strong class="text-indigo-600">Aprovado</strong> utiliza um
                  método de revisão espaçada para maximizar a retenção do
                  conteúdo estudado:
                </p>
                <ul class="space-y-2 mt-4">
                  <li class="flex items-start">
                    <span
                      class="inline-flex items-center justify-center rounded-full bg-indigo-100 h-6 w-6 text-xs font-medium text-indigo-600 mr-2 mt-0.5"
                      >1</span
                    >
                    <span
                      ><strong class="text-indigo-600">1ª revisão:</strong> 24
                      horas após o estudo inicial</span
                    >
                  </li>
                  <li class="flex items-start">
                    <span
                      class="inline-flex items-center justify-center rounded-full bg-indigo-100 h-6 w-6 text-xs font-medium text-indigo-600 mr-2 mt-0.5"
                      >2</span
                    >
                    <span
                      ><strong class="text-indigo-600">2ª revisão:</strong> 7
                      dias após a 1ª revisão</span
                    >
                  </li>
                  <li class="flex items-start">
                    <span
                      class="inline-flex items-center justify-center rounded-full bg-indigo-100 h-6 w-6 text-xs font-medium text-indigo-600 mr-2 mt-0.5"
                      >3</span
                    >
                    <span
                      ><strong class="text-indigo-600">3ª revisão:</strong> 30
                      dias após a 2ª revisão</span
                    >
                  </li>
                  <li class="flex items-start">
                    <span
                      class="inline-flex items-center justify-center rounded-full bg-indigo-100 h-6 w-6 text-xs font-medium text-indigo-600 mr-2 mt-0.5"
                      >4</span
                    >
                    <span
                      ><strong class="text-indigo-600">4ª revisão:</strong> 3
                      meses após a 3ª revisão</span
                    >
                  </li>
                  <li class="flex items-start">
                    <span
                      class="inline-flex items-center justify-center rounded-full bg-indigo-100 h-6 w-6 text-xs font-medium text-indigo-600 mr-2 mt-0.5"
                      >5</span
                    >
                    <span
                      ><strong class="text-indigo-600"
                        >Revisões recorrentes:</strong
                      >
                      a cada 6 meses após a 4ª revisão</span
                    >
                  </li>
                </ul>
                <p class="mt-4 text-indigo-600 font-medium">
                  Siga este cronograma para maximizar sua retenção de conteúdo e
                  aumentar suas chances de aprovação!
                </p>
              </div>
            </div>
          </div>

          <div
            class="overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
          >
            <div class="p-6">
              <h3
                class="mb-4 text-lg font-medium text-gray-900 flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-5 w-5 mr-2 text-indigo-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                  />
                </svg>
                Recursos Principais
              </h3>
              <ul class="space-y-4">
                <li class="flex items-start">
                  <div class="flex-shrink-0">
                    <div
                      class="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-100 text-indigo-600"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <h4 class="text-base font-medium text-gray-900">
                      Mapas Mentais
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">
                      Organize suas ideias visualmente
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <div class="flex-shrink-0">
                    <div
                      class="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-100 text-indigo-600"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <h4 class="text-base font-medium text-gray-900">
                      Gravações de Áudio
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">
                      Registre explicações verbais
                    </p>
                  </div>
                </li>
                <li class="flex items-start">
                  <div class="flex-shrink-0">
                    <div
                      class="flex items-center justify-center h-8 w-8 rounded-md bg-indigo-100 text-indigo-600"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <h4 class="text-base font-medium text-gray-900">
                      Revisão Espaçada
                    </h4>
                    <p class="mt-1 text-sm text-gray-600">
                      Otimize sua retenção de memória
                    </p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>
