<template>
  <Head title="Grupos de Disciplinas" />

  <AuthenticatedLayout>
    <template #header>
      <div
        class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
      >
        <h2
          class="text-lg sm:text-xl font-semibold leading-tight text-gray-800 dark:text-white"
        >
          Grupos de Disciplinas
        </h2>
        <Link
          :href="route('discipline-groups.create')"
          class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
        >
          <i class="ri-add-line mr-2 sm:mr-1"></i>
          Novo Grupo
        </Link>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Mensagem de sucesso -->
        <div
          v-if="$page.props.flash && $page.props.flash.success"
          class="mb-4 sm:mb-6 rounded-md bg-green-50 p-3 sm:p-4 dark:bg-green-900/20"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <i
                class="ri-check-circle-fill text-green-400 text-lg sm:text-xl"
              ></i>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800 dark:text-green-400">
                {{ $page.props.flash.success }}
              </p>
            </div>
          </div>
        </div>

        <!-- Mensagem de erro -->
        <div
          v-if="$page.props.flash && $page.props.flash.error"
          class="mb-4 sm:mb-6 rounded-md bg-red-50 p-3 sm:p-4 dark:bg-red-900/20"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <i
                class="ri-close-circle-fill text-red-400 text-lg sm:text-xl"
              ></i>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-red-800 dark:text-red-400">
                {{ $page.props.flash.error }}
              </p>
            </div>
          </div>
        </div>

        <!-- Lista de grupos -->
        <div
          v-if="disciplineGroups.length === 0"
          class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 p-8 sm:p-12 text-center"
        >
          <div
            class="mx-auto flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900"
          >
            <i
              class="ri-folder-line text-indigo-600 dark:text-indigo-400 text-xl sm:text-2xl"
            ></i>
          </div>
          <h3
            class="mt-2 text-sm sm:text-base font-medium text-gray-900 dark:text-white"
          >
            Nenhum grupo encontrado
          </h3>
          <p class="mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400">
            Crie seu primeiro grupo para organizar suas disciplinas.
          </p>
          <Link
            :href="route('discipline-groups.create')"
            class="mt-4 sm:mt-6 inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors"
          >
            <i class="ri-add-line -ml-0.5 mr-1.5"></i>
            Novo Grupo
          </Link>
        </div>

        <div v-else class="space-y-4 sm:space-y-6">
          <div
            v-for="group in disciplineGroups"
            :key="group.id"
            class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 shadow border border-gray-200 dark:border-gray-700"
          >
            <div class="bg-white dark:bg-gray-800 px-3 py-4 sm:px-4 sm:py-5">
              <div
                class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
              >
                <div class="flex items-center">
                  <div
                    class="flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-md bg-indigo-600 text-white flex-shrink-0"
                  >
                    <i class="ri-folder-line text-lg sm:text-2xl"></i>
                  </div>
                  <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                    <h3
                      class="text-base sm:text-lg font-medium leading-6 text-gray-900 dark:text-white"
                    >
                      <span class="truncate">{{ group.name }}</span>
                      <span
                        v-if="group.is_default"
                        class="ml-2 inline-block rounded-full bg-indigo-100 dark:bg-indigo-900 px-2 py-0.5 text-xs font-medium text-indigo-800 dark:text-indigo-300"
                      >
                        Padrão
                      </span>
                    </h3>
                    <p
                      v-if="group.description"
                      class="mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400 line-clamp-2"
                    >
                      {{ group.description }}
                    </p>
                    <p
                      class="mt-1 text-xs sm:text-sm text-gray-500 dark:text-gray-400"
                    >
                      {{ group.disciplines.length }} disciplinas neste grupo
                    </p>
                  </div>
                </div>
                <div
                  class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2"
                >
                  <Link
                    :href="route('discipline-groups.show', group.id)"
                    class="inline-flex items-center justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                  >
                    <i class="ri-eye-line mr-1 sm:mr-0"></i>
                    <span class="sm:hidden">Ver</span>
                  </Link>
                  <Link
                    :href="route('discipline-groups.edit', group.id)"
                    class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-colors"
                  >
                    <i class="ri-edit-line mr-1 sm:mr-0"></i>
                    <span class="sm:hidden">Editar</span>
                  </Link>
                  <button
                    v-if="!group.is_default"
                    @click="confirmDelete(group)"
                    class="inline-flex items-center justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600 transition-colors"
                  >
                    <i class="ri-delete-bin-line mr-1 sm:mr-0"></i>
                    <span class="sm:hidden">Excluir</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmação de exclusão -->
    <Modal :show="showDeleteModal" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">Confirmar exclusão</h2>
        <p class="mt-1 text-sm text-gray-600">
          Tem certeza que deseja excluir o grupo "{{ groupToDelete?.name }}"?
          <br />
          <span class="font-medium text-red-600">
            As disciplinas deste grupo serão movidas para o grupo padrão.
          </span>
        </p>
        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeModal">Cancelar</SecondaryButton>
          <DangerButton @click="deleteGroup">Excluir</DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import DangerButton from "@/Components/DangerButton.vue";

const props = defineProps({
  disciplineGroups: Array,
});

const showDeleteModal = ref(false);
const groupToDelete = ref(null);

const confirmDelete = (group) => {
  groupToDelete.value = group;
  showDeleteModal.value = true;
};

const closeModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    groupToDelete.value = null;
  }, 300);
};

const deleteGroup = () => {
  router.delete(route("discipline-groups.destroy", groupToDelete.value.id), {
    onSuccess: () => {
      closeModal();
    },
  });
};
</script>
