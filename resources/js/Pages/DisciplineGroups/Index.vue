<template>
  <Head title="Grupos de Disciplinas" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Grupos de Disciplinas
        </h2>
        <Link
          :href="route('discipline-groups.create')"
          class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          Novo Grupo
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Mensagem de sucesso -->
        <div
          v-if="$page.props.flash && $page.props.flash.success"
          class="mb-6 rounded-md bg-green-50 p-4"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-green-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">
                {{ $page.props.flash.success }}
              </p>
            </div>
          </div>
        </div>

        <!-- Mensagem de erro -->
        <div
          v-if="$page.props.flash && $page.props.flash.error"
          class="mb-6 rounded-md bg-red-50 p-4"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-red-800">
                {{ $page.props.flash.error }}
              </p>
            </div>
          </div>
        </div>

        <!-- Lista de grupos -->
        <div
          v-if="disciplineGroups.length === 0"
          class="flex flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-white p-12 text-center"
        >
          <div
            class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-indigo-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
          </div>
          <h3 class="mt-2 text-sm font-medium text-gray-900">
            Nenhum grupo encontrado
          </h3>
          <p class="mt-1 text-sm text-gray-500">
            Crie seu primeiro grupo para organizar suas disciplinas.
          </p>
          <Link
            :href="route('discipline-groups.create')"
            class="mt-6 inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            <svg
              class="-ml-0.5 mr-1.5 h-5 w-5"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                d="M10.75 4.75a.75.75 0 00-1.5 0v4.5h-4.5a.75.75 0 000 1.5h4.5v4.5a.75.75 0 001.5 0v-4.5h4.5a.75.75 0 000-1.5h-4.5v-4.5z"
              />
            </svg>
            Novo Grupo
          </Link>
        </div>

        <div v-else class="space-y-6">
          <div
            v-for="group in disciplineGroups"
            :key="group.id"
            class="overflow-hidden rounded-lg bg-white shadow"
          >
            <div class="border-b border-gray-200 bg-white px-4 py-5 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="flex h-10 w-10 items-center justify-center rounded-md bg-indigo-600 text-white"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                      />
                    </svg>
                  </div>
                  <div class="ml-4">
                    <h3 class="text-lg font-medium leading-6 text-gray-900">
                      {{ group.name }}
                      <span
                        v-if="group.is_default"
                        class="ml-2 rounded-full bg-indigo-100 px-2.5 py-0.5 text-xs font-medium text-indigo-800"
                      >
                        Padrão
                      </span>
                    </h3>
                    <p
                      v-if="group.description"
                      class="mt-1 text-sm text-gray-500"
                    >
                      {{ group.description }}
                    </p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Link
                    :href="route('discipline-groups.show', group.id)"
                    class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                  >
                    Ver
                  </Link>
                  <Link
                    :href="route('discipline-groups.edit', group.id)"
                    class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                  >
                    Editar
                  </Link>
                  <button
                    v-if="!group.is_default"
                    @click="confirmDelete(group)"
                    class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
                  >
                    Excluir
                  </button>
                </div>
              </div>
              <div class="mt-4">
                <p class="text-sm text-gray-500">
                  {{ group.disciplines.length }} disciplinas neste grupo
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmação de exclusão -->
    <Modal :show="showDeleteModal" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">Confirmar exclusão</h2>
        <p class="mt-1 text-sm text-gray-600">
          Tem certeza que deseja excluir o grupo "{{ groupToDelete?.name }}"?
          <br />
          <span class="font-medium text-red-600">
            As disciplinas deste grupo serão movidas para o grupo padrão.
          </span>
        </p>
        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeModal">Cancelar</SecondaryButton>
          <DangerButton @click="deleteGroup">Excluir</DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import DangerButton from "@/Components/DangerButton.vue";

const props = defineProps({
  disciplineGroups: Array,
});

const showDeleteModal = ref(false);
const groupToDelete = ref(null);

const confirmDelete = (group) => {
  groupToDelete.value = group;
  showDeleteModal.value = true;
};

const closeModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    groupToDelete.value = null;
  }, 300);
};

const deleteGroup = () => {
  router.delete(route("discipline-groups.destroy", groupToDelete.value.id), {
    onSuccess: () => {
      closeModal();
    },
  });
};
</script>
