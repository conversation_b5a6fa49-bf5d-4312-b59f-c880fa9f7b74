<template>
  <Head :title="disciplineGroup.name" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          {{ disciplineGroup.name }}
          <span
            v-if="disciplineGroup.is_default"
            class="ml-2 rounded-full bg-indigo-100 px-2.5 py-0.5 text-xs font-medium text-indigo-800"
          >
            <PERSON><PERSON>ão
          </span>
        </h2>
        <div class="flex space-x-2">
          <Link
            :href="route('discipline-groups.index')"
            class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
          >
            Voltar
          </Link>
          <Link
            :href="route('discipline-groups.edit', disciplineGroup.id)"
            class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          >
            Editar
          </Link>
        </div>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Mensagem de sucesso -->
        <div
          v-if="$page.props.flash && $page.props.flash.success"
          class="mb-6 rounded-md bg-green-50 p-4"
        >
          <div class="flex">
            <div class="flex-shrink-0">
              <svg
                class="h-5 w-5 text-green-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-green-800">
                {{ $page.props.flash.success }}
              </p>
            </div>
          </div>
        </div>

        <!-- Informações do grupo -->
        <div class="mb-6 overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Informações do Grupo
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
              Detalhes e disciplinas associadas a este grupo.
            </p>
          </div>
          <div class="border-t border-gray-200">
            <dl>
              <div
                class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
              >
                <dt class="text-sm font-medium text-gray-500">Nome</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                  {{ disciplineGroup.name }}
                </dd>
              </div>
              <div
                class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
              >
                <dt class="text-sm font-medium text-gray-500">Descrição</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                  {{ disciplineGroup.description || "Sem descrição" }}
                </dd>
              </div>
              <div
                class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6"
              >
                <dt class="text-sm font-medium text-gray-500">
                  Número de Disciplinas
                </dt>
                <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                  {{ disciplines.length }}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Gráfico de Tempo de Estudo -->
        <div
          v-if="chartData.datasets && chartData.datasets.length > 0"
          class="mb-6 overflow-hidden bg-white shadow sm:rounded-lg"
        >
          <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
              Tempo de Estudo por Disciplina
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
              Visualize o tempo de estudo diário para as disciplinas deste
              grupo.
            </p>
          </div>
          <div class="border-t border-gray-200 p-6">
            <div style="height: 400px">
              <StudySessionChart :chart-data="chartData" />
            </div>
          </div>
        </div>

        <!-- Lista de disciplinas -->
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:px-6">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                Disciplinas neste Grupo
              </h3>
              <Link
                :href="route('disciplines.create')"
                class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
              >
                Nova Disciplina
              </Link>
            </div>
          </div>

          <div v-if="disciplines.length === 0" class="px-4 py-5 sm:px-6">
            <p class="text-sm text-gray-500">
              Nenhuma disciplina encontrada neste grupo.
            </p>
          </div>

          <div v-else class="border-t border-gray-200">
            <ul role="list" class="divide-y divide-gray-200">
              <li
                v-for="discipline in disciplines"
                :key="discipline.id"
                class="px-4 py-4 sm:px-6"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div
                      class="flex h-10 w-10 items-center justify-center rounded-md bg-indigo-100 text-indigo-700"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                      </svg>
                    </div>
                    <div class="ml-4">
                      <h4 class="text-lg font-medium text-gray-900">
                        {{ discipline.name }}
                      </h4>
                      <p
                        v-if="discipline.description"
                        class="text-sm text-gray-500"
                      >
                        {{ discipline.description }}
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-2">
                    <Link
                      :href="route('disciplines.show', discipline.id)"
                      class="rounded-md bg-indigo-50 px-3 py-2 text-sm font-medium text-indigo-700 hover:bg-indigo-100"
                    >
                      Ver Detalhes
                    </Link>
                  </div>
                </div>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    {{ discipline.lessons_count }} aulas
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import StudySessionChart from "@/Components/StudySessionChart.vue";

const props = defineProps({
  disciplineGroup: Object,
  disciplines: Array,
  chartData: Object,
});
</script>
