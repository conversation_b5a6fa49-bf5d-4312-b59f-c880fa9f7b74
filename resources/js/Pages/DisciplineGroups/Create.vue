<template>
  <Head title="Novo Grupo de Disciplinas" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Novo Grupo de Disciplinas
        </h2>
        <Link
          :href="route('discipline-groups.index')"
          class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
        >
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
          <div class="p-6">
            <form @submit.prevent="submit">
              <div class="mb-6">
                <InputLabel for="name" value="Nome do Grupo" />
                <TextInput
                  id="name"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.name"
                  required
                  autofocus
                  placeholder="Ex: Faculdade, Concurso, Trabalho..."
                />
                <InputError class="mt-2" :message="form.errors.name" />
              </div>

              <div class="mb-6">
                <InputLabel for="description" value="Descrição (opcional)" />
                <Textarea
                  id="description"
                  class="mt-1 block w-full"
                  v-model="form.description"
                  placeholder="Descreva brevemente o propósito deste grupo..."
                  rows="4"
                />
                <InputError class="mt-2" :message="form.errors.description" />
              </div>

              <div class="flex items-center justify-end">
                <Link
                  :href="route('discipline-groups.index')"
                  class="rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100"
                >
                  Cancelar
                </Link>
                <PrimaryButton
                  class="ml-4"
                  :class="{ 'opacity-25': form.processing }"
                  :disabled="form.processing"
                >
                  Criar Grupo
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import Textarea from "@/Components/Textarea.vue";

const form = useForm({
  name: "",
  description: "",
});

const submit = () => {
  form.post(route("discipline-groups.store"));
};
</script>
