<template>
  <Head title="Gerenciamento de Usuários" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-white">
          Gerenciamento de Usuários
        </h2>
        <div class="flex space-x-2">
          <Link
            :href="route('admin.users.pending')"
            class="inline-flex items-center rounded-md bg-yellow-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
          >
            <i class="ri-time-line mr-2"></i>
            Usuários Pendentes ({{ stats.pending }})
          </Link>
        </div>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Estatísticas -->
        <div class="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          <div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="ri-group-line text-2xl text-gray-400"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Total de Usuários
                    </dt>
                    <dd class="text-lg font-medium text-gray-900 dark:text-white">
                      {{ stats.total }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="ri-check-line text-2xl text-green-400"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Usuários Ativos
                    </dt>
                    <dd class="text-lg font-medium text-gray-900 dark:text-white">
                      {{ stats.active }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="ri-time-line text-2xl text-yellow-400"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Usuários Pendentes
                    </dt>
                    <dd class="text-lg font-medium text-gray-900 dark:text-white">
                      {{ stats.pending }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="ri-close-line text-2xl text-red-400"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Usuários Bloqueados
                    </dt>
                    <dd class="text-lg font-medium text-gray-900 dark:text-white">
                      {{ stats.blocked }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filtros -->
        <div class="mb-6 rounded-lg bg-white p-6 shadow dark:bg-gray-800">
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Buscar por nome ou e-mail
              </label>
              <input
                v-model="searchForm.search"
                type="text"
                placeholder="Digite o nome ou e-mail..."
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                @input="debouncedSearch"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Filtrar por status
              </label>
              <select
                v-model="searchForm.status"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                @change="search"
              >
                <option value="">Todos os status</option>
                <option value="active">Ativo</option>
                <option value="pending">Pendente</option>
                <option value="blocked">Bloqueado</option>
              </select>
            </div>
            <div class="flex items-end">
              <button
                @click="clearFilters"
                class="rounded-md bg-gray-600 px-4 py-2 text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              >
                Limpar Filtros
              </button>
            </div>
          </div>
        </div>

        <!-- Tabela de Usuários -->
        <div class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div class="px-4 py-5 sm:p-6">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      Usuário
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      E-mail
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      Papel
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      Data de Cadastro
                    </th>
                    <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                  <tr v-for="user in users.data" :key="user.id">
                    <td class="whitespace-nowrap px-6 py-4">
                      <div class="flex items-center">
                        <div class="h-10 w-10 flex-shrink-0">
                          <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                            <span class="text-sm font-medium text-indigo-800">
                              {{ user.name.charAt(0).toUpperCase() }}
                            </span>
                          </div>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ user.name }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      {{ user.email }}
                    </td>
                    <td class="whitespace-nowrap px-6 py-4">
                      <span
                        class="inline-flex rounded-full px-2 text-xs font-semibold leading-5"
                        :class="{
                          'bg-purple-100 text-purple-800': user.role === 'admin',
                          'bg-gray-100 text-gray-800': user.role === 'user'
                        }"
                      >
                        {{ user.role === 'admin' ? 'Administrador' : 'Usuário' }}
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-6 py-4">
                      <span
                        class="inline-flex rounded-full px-2 text-xs font-semibold leading-5"
                        :class="{
                          'bg-green-100 text-green-800': user.status === 'active',
                          'bg-yellow-100 text-yellow-800': user.status === 'pending',
                          'bg-red-100 text-red-800': user.status === 'blocked'
                        }"
                      >
                        {{ getStatusLabel(user.status) }}
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      {{ formatDate(user.created_at) }}
                    </td>
                    <td class="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <div class="flex justify-end space-x-2">
                        <!-- Aprovar usuário pendente -->
                        <button
                          v-if="user.status === 'pending'"
                          @click="approveUser(user)"
                          class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          title="Aprovar usuário"
                        >
                          <i class="ri-check-line text-lg"></i>
                        </button>

                        <!-- Bloquear/Desbloquear usuário -->
                        <button
                          v-if="user.status === 'active' && user.role !== 'admin'"
                          @click="blockUser(user)"
                          class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="Bloquear usuário"
                        >
                          <i class="ri-forbid-line text-lg"></i>
                        </button>

                        <button
                          v-if="user.status === 'blocked'"
                          @click="unblockUser(user)"
                          class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                          title="Desbloquear usuário"
                        >
                          <i class="ri-check-line text-lg"></i>
                        </button>

                        <!-- Excluir usuário -->
                        <button
                          v-if="user.role !== 'admin' && user.id !== $page.props.auth.user.id"
                          @click="confirmDeleteUser(user)"
                          class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="Excluir usuário"
                        >
                          <i class="ri-delete-bin-line text-lg"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Paginação -->
            <div v-if="users.links.length > 3" class="mt-6">
              <nav class="flex items-center justify-between">
                <div class="flex flex-1 justify-between sm:hidden">
                  <Link
                    v-if="users.prev_page_url"
                    :href="users.prev_page_url"
                    class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Anterior
                  </Link>
                  <Link
                    v-if="users.next_page_url"
                    :href="users.next_page_url"
                    class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Próximo
                  </Link>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                      Mostrando
                      <span class="font-medium">{{ users.from }}</span>
                      até
                      <span class="font-medium">{{ users.to }}</span>
                      de
                      <span class="font-medium">{{ users.total }}</span>
                      resultados
                    </p>
                  </div>
                  <div>
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm">
                      <Link
                        v-for="link in users.links"
                        :key="link.label"
                        :href="link.url"
                        v-html="link.label"
                        :class="[
                          'relative inline-flex items-center px-4 py-2 text-sm font-medium',
                          link.active
                            ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                          link.url ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'
                        ]"
                      />
                    </nav>
                  </div>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Confirmação -->
    <ConfirmationModal
      :show="showDeleteModal"
      title="Excluir Usuário"
      :message="`Tem certeza que deseja excluir o usuário ${userToDelete?.name}? Esta ação não pode ser desfeita.`"
      confirm-button-text="Excluir"
      @confirm="deleteUser"
      @close="showDeleteModal = false"
    />
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import ConfirmationModal from "@/Components/ConfirmationModal.vue";

const props = defineProps({
  users: Object,
  stats: Object,
  recentUsers: Array,
  filters: Object,
});

const showDeleteModal = ref(false);
const userToDelete = ref(null);

const searchForm = reactive({
  search: props.filters.search || "",
  status: props.filters.status || "",
});

let searchTimeout = null;

const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    search();
  }, 300);
};

const search = () => {
  router.get(route('admin.users.index'), searchForm, {
    preserveState: true,
    replace: true,
  });
};

const clearFilters = () => {
  searchForm.search = "";
  searchForm.status = "";
  search();
};

const getStatusLabel = (status) => {
  const labels = {
    active: "Ativo",
    pending: "Pendente",
    blocked: "Bloqueado",
  };
  return labels[status] || status;
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString("pt-BR");
};

const approveUser = (user) => {
  router.post(route('admin.users.approve', user.id), {}, {
    preserveScroll: true,
  });
};

const blockUser = (user) => {
  router.post(route('admin.users.block', user.id), {}, {
    preserveScroll: true,
  });
};

const unblockUser = (user) => {
  router.post(route('admin.users.unblock', user.id), {}, {
    preserveScroll: true,
  });
};

const confirmDeleteUser = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

const deleteUser = () => {
  if (userToDelete.value) {
    router.delete(route('admin.users.destroy', userToDelete.value.id), {
      preserveScroll: true,
      onSuccess: () => {
        showDeleteModal.value = false;
        userToDelete.value = null;
      },
    });
  }
};
</script>
