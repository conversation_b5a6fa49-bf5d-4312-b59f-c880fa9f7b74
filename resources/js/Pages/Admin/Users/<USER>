<template>
  <Head title="Usuários Pendentes" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-white">
          Usuários Pendentes de Aprovação
        </h2>
        <div class="flex space-x-2">
          <Link
            :href="route('admin.users.index')"
            class="inline-flex items-center rounded-md bg-gray-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            <i class="ri-arrow-left-line mr-2"></i>
            Voltar para Usuários
          </Link>
        </div>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div v-if="users.data.length === 0" class="text-center py-12">
          <i class="ri-check-line text-6xl text-green-400 mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Nenhum usuário pendente
          </h3>
          <p class="text-gray-600 dark:text-gray-400">
            Todos os usuários foram aprovados ou não há novos cadastros pendentes.
          </p>
        </div>

        <div v-else class="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
          <div class="px-4 py-5 sm:p-6">
            <div class="mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                {{ users.total }} usuário(s) aguardando aprovação
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Revise e aprove os novos usuários para que possam acessar o sistema.
              </p>
            </div>

            <div class="space-y-4">
              <div
                v-for="user in users.data"
                :key="user.id"
                class="rounded-lg border border-gray-200 bg-gray-50 p-6 dark:border-gray-700 dark:bg-gray-700"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div class="h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                      <span class="text-lg font-medium text-yellow-800">
                        {{ user.name.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                    <div>
                      <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                        {{ user.name }}
                      </h4>
                      <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ user.email }}
                      </p>
                      <p class="text-xs text-gray-500 dark:text-gray-500">
                        Cadastrado em {{ formatDate(user.created_at) }}
                      </p>
                    </div>
                  </div>
                  <div class="flex space-x-3">
                    <button
                      @click="approveUser(user)"
                      class="inline-flex items-center rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    >
                      <i class="ri-check-line mr-2"></i>
                      Aprovar
                    </button>
                    <button
                      @click="blockUser(user)"
                      class="inline-flex items-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                    >
                      <i class="ri-forbid-line mr-2"></i>
                      Bloquear
                    </button>
                    <button
                      @click="confirmDeleteUser(user)"
                      class="inline-flex items-center rounded-md bg-gray-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                    >
                      <i class="ri-delete-bin-line mr-2"></i>
                      Excluir
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Paginação -->
            <div v-if="users.links.length > 3" class="mt-6">
              <nav class="flex items-center justify-between">
                <div class="flex flex-1 justify-between sm:hidden">
                  <Link
                    v-if="users.prev_page_url"
                    :href="users.prev_page_url"
                    class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Anterior
                  </Link>
                  <Link
                    v-if="users.next_page_url"
                    :href="users.next_page_url"
                    class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Próximo
                  </Link>
                </div>
                <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                      Mostrando
                      <span class="font-medium">{{ users.from }}</span>
                      até
                      <span class="font-medium">{{ users.to }}</span>
                      de
                      <span class="font-medium">{{ users.total }}</span>
                      resultados
                    </p>
                  </div>
                  <div>
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm">
                      <Link
                        v-for="link in users.links"
                        :key="link.label"
                        :href="link.url"
                        v-html="link.label"
                        :class="[
                          'relative inline-flex items-center px-4 py-2 text-sm font-medium',
                          link.active
                            ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                          link.url ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'
                        ]"
                      />
                    </nav>
                  </div>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de Confirmação -->
    <ConfirmationModal
      :show="showDeleteModal"
      title="Excluir Usuário"
      :message="`Tem certeza que deseja excluir o usuário ${userToDelete?.name}? Esta ação não pode ser desfeita.`"
      confirm-button-text="Excluir"
      @confirm="deleteUser"
      @close="showDeleteModal = false"
    />
  </AuthenticatedLayout>
</template>

<script setup>
import { ref } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import ConfirmationModal from "@/Components/ConfirmationModal.vue";

const props = defineProps({
  users: Object,
});

const showDeleteModal = ref(false);
const userToDelete = ref(null);

const formatDate = (date) => {
  return new Date(date).toLocaleDateString("pt-BR");
};

const approveUser = (user) => {
  router.post(route('admin.users.approve', user.id), {}, {
    preserveScroll: true,
  });
};

const blockUser = (user) => {
  router.post(route('admin.users.block', user.id), {}, {
    preserveScroll: true,
  });
};

const confirmDeleteUser = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

const deleteUser = () => {
  if (userToDelete.value) {
    router.delete(route('admin.users.destroy', userToDelete.value.id), {
      preserveScroll: true,
      onSuccess: () => {
        showDeleteModal.value = false;
        userToDelete.value = null;
      },
    });
  }
};
</script>
