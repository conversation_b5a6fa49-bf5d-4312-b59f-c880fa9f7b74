<template>
  <Head title="Revisão" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div
            class="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
          >
            <i class="ri-time-line text-2xl"></i>
          </div>
          <div>
            <h2
              class="text-xl font-semibold leading-tight text-indigo-800 dark:text-white"
            >
              {{ getRevisionTypeLabel(revision.revision_type) }}
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ revision.lesson.title }}
            </p>
          </div>
        </div>
        <Link
          :href="route('revisions.index')"
          class="flex items-center rounded-lg border border-transparent bg-indigo-50 px-4 py-2 text-sm font-medium text-indigo-700 transition-colors hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-950/50 dark:text-indigo-300 dark:hover:bg-indigo-950/70"
        >
          <i class="ri-arrow-left-line mr-1.5"></i>
          Voltar para Revisões
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Informações da Revisão"
              v-model="sectionsExpanded.revisionInfo"
            >
              <template #icon>
                <i
                  class="ri-information-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.revisionInfo" class="px-6 py-5">
            <div
              class="mb-6 rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
            >
              <h3
                class="mb-4 text-lg font-medium text-gray-900 dark:text-white"
              >
                Roteiro de Estudo
              </h3>
              <ul>
                <li>1º Revisar os flashcards</li>
                <li>
                  2º Ler o mapa mental e gravar, caso ainda não tenha feito
                </li>
                <li>
                  3º Ouvir os áudios de revisão, se já estiverem disponíveis
                </li>
                <li>4º Responder as questões de revisão</li>
                <li>
                  5º Resolver 10 questões do tema, acessando os Links de
                  Questões
                </li>
              </ul>
            </div>
            <dl
              class="grid grid-cols-1 gap-x-6 gap-y-6 sm:grid-cols-2 lg:grid-cols-3"
            >
              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <i class="ri-book-open-line mr-1.5 text-indigo-500"></i>
                  Aula
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  <Link
                    :href="route('lessons.show', revision.lesson.id)"
                    class="flex items-center text-indigo-600 transition-colors hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
                  >
                    {{ revision.lesson.title }}
                    <i class="ri-external-link-line ml-1 text-sm"></i>
                  </Link>
                </dd>
              </div>

              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <i class="ri-graduation-cap-line mr-1.5 text-indigo-500"></i>
                  Disciplina
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  <Link
                    :href="
                      route('disciplines.show', revision.lesson.discipline.id)
                    "
                    class="flex items-center text-indigo-600 transition-colors hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
                  >
                    {{ revision.lesson.discipline.name }}
                    <i class="ri-external-link-line ml-1 text-sm"></i>
                  </Link>
                </dd>
              </div>

              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <i
                    class="ri-lightbulb-line mr-1.5 h-4 w-4 text-indigo-500"
                  ></i>
                  Tipo de Revisão
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  {{ getRevisionTypeLabel(revision.revision_type) }}
                </dd>
              </div>

              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <i
                    class="ri-calendar-line mr-1.5 h-4 w-4 text-indigo-500"
                  ></i>
                  Data Agendada
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  {{ formatDate(revision.scheduled_date) }}
                </dd>
              </div>

              <div
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <i
                    class="ri-checkbox-circle-line mr-1.5 h-4 w-4 text-indigo-500"
                  ></i>
                  Status
                </dt>
                <dd class="mt-2 flex items-center text-sm">
                  <span
                    :class="{
                      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400':
                        revision.is_completed,
                      'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400':
                        !revision.is_completed,
                    }"
                    class="inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium"
                  >
                    <i
                      v-if="revision.is_completed"
                      class="ri-check-line mr-1 text-sm"
                    ></i>
                    <i v-else class="ri-time-line mr-1 text-sm"></i>
                    {{ revision.is_completed ? "Concluída" : "Pendente" }}
                  </span>
                </dd>
              </div>

              <div
                v-if="revision.completed_date"
                class="rounded-lg bg-gray-50 p-4 transition-all duration-200 hover:bg-gray-100 dark:bg-zinc-800/50 dark:hover:bg-zinc-800"
              >
                <dt
                  class="flex items-center text-sm font-medium text-gray-500 dark:text-gray-400"
                >
                  <i
                    class="ri-calendar-check-line mr-1.5 h-4 w-4 text-indigo-500"
                  ></i>
                  Data de Conclusão
                </dt>
                <dd
                  class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200"
                >
                  {{ formatDate(revision.completed_date) }}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div
          v-if="revision.lesson.mind_map"
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Mapa Mental"
              v-model="sectionsExpanded.mindMap"
            >
              <template #icon>
                <i
                  class="ri-mind-map-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.mindMap" class="px-6 py-5">
            <div
              class="flex justify-center rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-zinc-800"
            >
              <div class="w-full max-w-4xl">
                <MindMapPreview :data="mindMapData" />
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="
            revision.lesson.audio_recordings &&
            revision.lesson.audio_recordings.length > 0
          "
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Áudios de Revisão"
              v-model="sectionsExpanded.audioRecordings"
            >
              <template #icon>
                <i
                  class="ri-mic-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.audioRecordings" class="px-6 py-5">
            <ul class="space-y-4">
              <li
                v-for="audio in revision.lesson.audio_recordings"
                :key="audio.id"
                class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-700 dark:bg-zinc-800"
              >
                <div
                  class="border-b border-gray-200 bg-gray-50 px-4 py-3 dark:border-gray-700 dark:bg-zinc-800/80"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <h5
                        class="text-sm font-medium text-gray-900 dark:text-gray-100"
                      >
                        {{ audio.title }}
                      </h5>
                      <p
                        v-if="audio.duration_seconds"
                        class="mt-1 text-xs text-gray-500 dark:text-gray-400"
                      >
                        Duração: {{ formatDuration(audio.duration_seconds) }}
                      </p>
                    </div>
                  </div>
                </div>

                <div class="custom-audio-player p-4">
                  <div class="flex items-center space-x-4">
                    <button
                      @click="togglePlay(audio)"
                      class="play-button flex h-12 w-12 items-center justify-center rounded-full bg-indigo-600 text-white shadow-md transition-all duration-200 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                    >
                      <i
                        :class="
                          isPlaying[audio.id] ? 'ri-pause-line' : 'ri-play-line'
                        "
                        class="text-2xl"
                      ></i>
                    </button>

                    <div class="flex-grow">
                      <div
                        class="relative h-2.5 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700"
                      >
                        <div
                          class="absolute top-0 left-0 h-full rounded-full bg-indigo-600 transition-all duration-100 dark:bg-indigo-500"
                          :style="{
                            width: `${audioProgress[audio.id] || 0}%`,
                          }"
                        ></div>
                      </div>

                      <div
                        class="mt-1.5 flex justify-between text-xs text-gray-500 dark:text-gray-400"
                      >
                        <span>{{
                          formatTime(audioCurrentTime[audio.id] || 0)
                        }}</span>
                        <span>{{
                          formatDuration(audio.duration_seconds)
                        }}</span>
                      </div>
                    </div>

                    <div class="audio-controls flex items-center space-x-4">
                      <div class="playback-speed-control relative">
                        <button
                          @click="toggleSpeedMenu(audio)"
                          class="flex items-center text-xs font-medium text-gray-600 hover:text-gray-800 focus:outline-none dark:text-gray-400 dark:hover:text-gray-300"
                        >
                          <i class="ri-speed-line mr-1"></i>
                          {{ audioPlaybackSpeed[audio.id] || "1.0" }}x
                        </button>

                        <div
                          v-if="speedMenuOpen[audio.id]"
                          class="absolute bottom-full left-0 mb-2 w-24 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-zinc-800 dark:ring-white/10 z-10"
                        >
                          <div
                            class="py-1"
                            role="menu"
                            aria-orientation="vertical"
                          >
                            <button
                              v-for="speed in [
                                0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0,
                              ]"
                              :key="speed"
                              @click="changePlaybackSpeed(audio, speed)"
                              class="block w-full px-4 py-2 text-left text-xs text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-zinc-700"
                              :class="{
                                'font-bold text-indigo-600 dark:text-indigo-400':
                                  (audioPlaybackSpeed[audio.id] || 1.0) ===
                                  speed,
                              }"
                              role="menuitem"
                            >
                              {{ speed }}x
                            </button>
                          </div>
                        </div>
                      </div>

                      <div class="volume-control flex items-center space-x-2">
                        <button
                          @click="toggleMute(audio)"
                          class="text-gray-600 hover:text-gray-800 focus:outline-none dark:text-gray-400 dark:hover:text-gray-300"
                        >
                          <i
                            :class="
                              isMuted[audio.id]
                                ? 'ri-volume-mute-line'
                                : 'ri-volume-up-line'
                            "
                            class="text-xl"
                          ></i>
                        </button>
                        <input
                          type="range"
                          min="0"
                          max="100"
                          :value="audioVolume[audio.id] || 100"
                          @input="updateVolume($event, audio)"
                          class="h-1.5 w-20 cursor-pointer appearance-none rounded-lg bg-gray-300 dark:bg-gray-600"
                        />
                      </div>
                    </div>
                  </div>

                  <audio
                    :ref="
                      (el) => {
                        if (el) audioElements[audio.id] = el;
                      }
                    "
                    :src="audio.audio_url"
                    preload="metadata"
                    @timeupdate="updateProgress(audio)"
                    @ended="audioEnded(audio)"
                    class="hidden"
                  ></audio>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div
          v-if="
            revision.lesson.question_links &&
            revision.lesson.question_links.length > 0
          "
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Links de Questões"
              v-model="sectionsExpanded.questionLinks"
            >
              <template #icon>
                <i
                  class="ri-question-answer-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.questionLinks" class="px-6 py-5">
            <ul class="space-y-3">
              <li
                v-for="link in revision.lesson.question_links"
                :key="link.id"
                class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-700 dark:bg-zinc-800"
              >
                <div class="flex items-center justify-between p-4">
                  <div class="flex-1 pr-4">
                    <a
                      :href="link.url"
                      target="_blank"
                      rel="noopener noreferrer"
                      class="truncate text-xs text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
                    >
                      <h5
                        class="text-sm font-medium text-gray-900 dark:text-gray-100"
                      >
                        <i class="ri-survey-line"></i>
                        {{ link.title }}
                      </h5>
                    </a>
                  </div>
                  <a
                    :href="link.url"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="flex items-center rounded-lg bg-indigo-50 px-3 py-2 text-xs font-medium text-indigo-700 transition-colors hover:bg-indigo-100 dark:bg-indigo-900/30 dark:text-indigo-300 dark:hover:bg-indigo-900/50"
                  >
                    <i class="ri-external-link-line mr-1.5"></i>
                    Abrir
                  </a>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div
          v-if="revision.lesson.notes && revision.lesson.notes.length > 0"
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader title="Anotações" v-model="sectionsExpanded.notes">
              <template #icon>
                <i
                  class="ri-sticky-note-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.notes" class="px-6 py-5">
            <NotesList
              :notes="revision.lesson.notes"
              :lesson-id="revision.lesson.id"
              :can-edit="false"
            />
          </div>
        </div>

        <div
          v-if="
            revision.lesson.flashcards && revision.lesson.flashcards.length > 0
          "
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Flashcards"
              v-model="sectionsExpanded.flashcards"
            >
              <template #icon>
                <i
                  class="ri-bank-card-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.flashcards" class="px-6 py-5">
            <FlashcardList
              :flashcards="revision.lesson.flashcards"
              :lesson-id="revision.lesson.id"
              :can-edit="false"
            />
          </div>
        </div>

        <div
          v-if="
            revision.lesson.questions && revision.lesson.questions.length > 0
          "
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Questões para Revisão"
              v-model="sectionsExpanded.questions"
            >
              <template #icon>
                <i
                  class="ri-question-mark mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.questions" class="px-6 py-5">
            <QuestionList
              :questions="revision.lesson.questions"
              :lesson-id="revision.lesson.id"
              :can-edit="false"
            />
          </div>
        </div>

        <div
          class="mb-8 overflow-hidden rounded-xl bg-white shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div
            class="border-b border-gray-200 bg-indigo-50 px-6 py-4 dark:border-gray-700 dark:bg-indigo-950/30"
          >
            <SectionHeader
              title="Anotações da Revisão"
              v-model="sectionsExpanded.revisionNotes"
            >
              <template #icon>
                <i
                  class="ri-pencil-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
              </template>
            </SectionHeader>
          </div>
          <div v-if="sectionsExpanded.revisionNotes" class="px-6 py-5">
            <form @submit.prevent="updateRevision">
              <div class="mb-6">
                <label
                  for="notes"
                  class="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Suas anotações
                </label>
                <Textarea
                  id="notes"
                  v-model="form.notes"
                  rows="5"
                  class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
                  placeholder="Adicione suas anotações sobre esta revisão..."
                ></Textarea>
              </div>

              <div
                class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
              >
                <div class="flex items-center">
                  <div class="relative flex items-start">
                    <div class="flex h-5 items-center">
                      <input
                        id="is_completed"
                        type="checkbox"
                        class="h-5 w-5 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700"
                        v-model="form.is_completed"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label
                        for="is_completed"
                        class="font-medium text-gray-700 dark:text-gray-300"
                      >
                        Marcar como concluída
                      </label>
                      <p class="text-gray-500 dark:text-gray-400">
                        Ao marcar, a revisão será considerada completa e a data
                        de conclusão será registrada.
                      </p>
                    </div>
                  </div>
                </div>

                <PrimaryButton
                  :class="{ 'opacity-25': form.processing }"
                  :disabled="form.processing"
                  class="bg-indigo-600 px-6 py-3 hover:bg-indigo-700 focus:ring-indigo-500 dark:bg-indigo-700 dark:hover:bg-indigo-600"
                >
                  <svg
                    v-if="form.processing"
                    class="mr-2 h-4 w-4 animate-spin"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      class="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      stroke-width="4"
                    ></circle>
                    <path
                      class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <span>{{
                    form.processing ? "Salvando..." : "Salvar Anotações"
                  }}</span>
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { Head, Link, useForm } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import Textarea from "@/Components/Textarea.vue";
import MindMapPreview from "@/Components/MindMapPreview.vue";
import QuestionList from "@/Components/QuestionList.vue";
import FlashcardList from "@/Components/FlashcardList.vue";
import NotesList from "@/Components/NotesList.vue";
import SectionHeader from "@/Components/SectionHeader.vue";

const props = defineProps({
  revision: Object,
});

// Section expansion state
const sectionsExpanded = ref({
  revisionInfo: true, // Informações da Revisão - expanded by default
  mindMap: false, // Mapa Mental
  questionLinks: false, // Links de Questões
  audioRecordings: false, // Áudios de Revisão
  notes: false, // Anotações
  flashcards: false, // Flashcards
  questions: false, // Questões
  revisionNotes: false, // Anotações da Revisão
});

const mindMapData = ref({
  content: "",
});

const form = useForm({
  is_completed: props.revision.is_completed,
  notes: props.revision.notes || "",
});

// Player de áudio personalizado
const audioElements = ref({});
const isPlaying = ref({});
const isMuted = ref({});
const audioProgress = ref({});
const audioCurrentTime = ref({});
const audioVolume = ref({});
const audioPlaybackSpeed = ref({});
const speedMenuOpen = ref({});

// Função para alternar entre play e pause
const togglePlay = (audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  if (isPlaying.value[audio.id]) {
    audioElement.pause();
    isPlaying.value[audio.id] = false;
  } else {
    // Pausar todos os outros áudios que estejam tocando
    Object.keys(audioElements.value).forEach((id) => {
      if (id !== audio.id.toString() && audioElements.value[id]) {
        audioElements.value[id].pause();
        isPlaying.value[id] = false;
      }
    });

    audioElement.play().catch((error) => {
      console.error("Erro ao reproduzir áudio:", error);
    });
    isPlaying.value[audio.id] = true;
  }
};

// Função para atualizar o progresso da reprodução
const updateProgress = (audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  const progress = (audioElement.currentTime / audioElement.duration) * 100;
  audioProgress.value[audio.id] = progress;
  audioCurrentTime.value[audio.id] = audioElement.currentTime;
};

// Função para lidar com o fim da reprodução
const audioEnded = (audio) => {
  isPlaying.value[audio.id] = false;
  audioProgress.value[audio.id] = 0;
  audioCurrentTime.value[audio.id] = 0;
};

// Função para alternar entre mudo e com som
const toggleMute = (audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  audioElement.muted = !audioElement.muted;
  isMuted.value[audio.id] = audioElement.muted;
};

// Função para atualizar o volume
const updateVolume = (event, audio) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  const volume = event.target.value / 100;
  audioElement.volume = volume;
  audioVolume.value[audio.id] = event.target.value;

  // Se o volume for 0, considerar como mudo
  if (volume === 0) {
    isMuted.value[audio.id] = true;
  } else if (isMuted.value[audio.id]) {
    isMuted.value[audio.id] = false;
    audioElement.muted = false;
  }
};

// Função para alternar o menu de velocidade
const toggleSpeedMenu = (audio) => {
  // Fechar todos os outros menus de velocidade
  Object.keys(speedMenuOpen.value).forEach((id) => {
    if (id !== audio.id.toString()) {
      speedMenuOpen.value[id] = false;
    }
  });

  // Alternar o menu atual
  speedMenuOpen.value[audio.id] = !speedMenuOpen.value[audio.id];

  // Adicionar um event listener para fechar o menu quando clicar fora dele
  if (speedMenuOpen.value[audio.id]) {
    setTimeout(() => {
      const closeMenu = (e) => {
        const speedMenus = document.querySelectorAll(".playback-speed-control");
        let clickedInside = false;

        speedMenus.forEach((menu) => {
          if (menu.contains(e.target)) {
            clickedInside = true;
          }
        });

        if (!clickedInside) {
          speedMenuOpen.value[audio.id] = false;
          document.removeEventListener("click", closeMenu);
        }
      };

      document.addEventListener("click", closeMenu);
    }, 0);
  }
};

// Função para alterar a velocidade de reprodução
const changePlaybackSpeed = (audio, speed) => {
  const audioElement = audioElements.value[audio.id];
  if (!audioElement) return;

  audioElement.playbackRate = speed;
  audioPlaybackSpeed.value[audio.id] = speed;
  speedMenuOpen.value[audio.id] = false;
};

const formatDate = (dateString) => {
  if (!dateString) return "";
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

const formatDuration = (seconds) => {
  if (!seconds) return "00:00";
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

// Alias para formatDuration para uso no player de áudio
const formatTime = formatDuration;

const getRevisionTypeLabel = (type) => {
  const labels = {
    first: "1ª Revisão",
    second: "2ª Revisão",
    third: "3ª Revisão",
    fourth: "4ª Revisão",
    recurring: "Revisão Recorrente",
    weekly: "Revisão Semanal",
  };

  return labels[type] || "Revisão";
};

const updateRevision = () => {
  form.put(route("revisions.update", props.revision.id));
};

onMounted(() => {
  // Agora o conteúdo já está em formato de texto (Markdown)
  if (
    props.revision.lesson.mind_map &&
    props.revision.lesson.mind_map.content
  ) {
    mindMapData.value = {
      content: props.revision.lesson.mind_map.content,
    };
  } else {
    // Conteúdo padrão se não houver nada
    mindMapData.value = {
      content: `# ${props.revision.lesson.title}\n## Sem conteúdo disponível`,
    };
  }

  // Inicializar a velocidade de reprodução para todos os áudios
  if (props.revision.lesson.audio_recordings) {
    props.revision.lesson.audio_recordings.forEach((audio) => {
      audioPlaybackSpeed.value[audio.id] = 1.0;
    });
  }
});
</script>

<style scoped>
/* Estilos para o player de áudio personalizado */
.custom-audio-player {
  transition: all 0.3s ease;
}

.custom-audio-player:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Estilizar o controle deslizante de volume */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 6px;
  background: #e5e7eb;
  outline: none;
}

.dark input[type="range"] {
  background: #4b5563;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: #4338ca;
  transform: scale(1.1);
}

.dark input[type="range"]::-webkit-slider-thumb {
  background: #6366f1;
}

.dark input[type="range"]::-webkit-slider-thumb:hover {
  background: #818cf8;
}

input[type="range"]::-moz-range-thumb {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

input[type="range"]::-moz-range-thumb:hover {
  background: #4338ca;
  transform: scale(1.1);
}

.dark input[type="range"]::-moz-range-thumb {
  background: #6366f1;
}

.dark input[type="range"]::-moz-range-thumb:hover {
  background: #818cf8;
}

/* Estilos para o controle de velocidade */
.playback-speed-control button {
  transition: all 0.2s ease;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
}

.playback-speed-control button:hover {
  background-color: #f3f4f6;
}

.dark .playback-speed-control button:hover {
  background-color: #374151;
}

/* Animação para o botão de play */
.play-button {
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2),
    0 2px 4px -1px rgba(79, 70, 229, 0.1);
}

.play-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 10px -1px rgba(79, 70, 229, 0.3),
    0 2px 4px -1px rgba(79, 70, 229, 0.2);
}

.play-button:active {
  transform: scale(0.95);
}

.dark .play-button {
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.3),
    0 2px 4px -1px rgba(99, 102, 241, 0.2);
}

.dark .play-button:hover {
  box-shadow: 0 6px 10px -1px rgba(99, 102, 241, 0.4),
    0 2px 4px -1px rgba(99, 102, 241, 0.3);
}
</style>
