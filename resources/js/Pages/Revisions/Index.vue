<template>
  <Head title="Revisões" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Revisões
        </h2>
        <Link
          :href="route('calendar.index')"
          class="rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
        >
          Ver Calendário
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <!-- Banner informativo -->
        <div
          class="mb-8 bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-lg shadow-md overflow-hidden"
        >
          <div
            class="px-6 py-5 sm:px-8 sm:py-6 flex flex-col md:flex-row items-center justify-between"
          >
            <div class="flex items-center mb-4 md:mb-0">
              <div class="flex-shrink-0 bg-white/20 rounded-full p-2 mr-4">
                <i class="ri-time-line text-white text-3xl"></i>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-white">
                  Revisões Espaçadas
                </h3>
                <p class="text-indigo-100 text-sm">
                  Maximize sua retenção de conhecimento com revisões programadas
                </p>
              </div>
            </div>
            <Link
              :href="route('calendar.index')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white transition-colors duration-150"
            >
              <i class="ri-calendar-line mr-1"></i>
              Ver Calendário
            </Link>
          </div>
        </div>

        <!-- Filtros -->
        <div
          class="mb-6 bg-white rounded-lg shadow-md p-4 border border-gray-100"
        >
          <div class="flex items-center">
            <div class="flex items-center">
              <i class="ri-filter-line text-indigo-500 mr-2"></i>
              <span class="text-sm font-medium text-gray-700"
                >Filtrar por:</span
              >
            </div>
            <div class="ml-4 flex-grow">
              <div class="flex flex-wrap gap-2">
                <button
                  @click="
                    filter = 'all';
                    applyFilter();
                  "
                  :class="[
                    'px-3 py-1.5 text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'all'
                      ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50',
                  ]"
                >
                  Todas
                </button>
                <button
                  @click="
                    filter = 'pending';
                    applyFilter();
                  "
                  :class="[
                    'px-3 py-1.5 text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'pending'
                      ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50',
                  ]"
                >
                  Pendentes
                </button>
                <button
                  @click="
                    filter = 'completed';
                    applyFilter();
                  "
                  :class="[
                    'px-3 py-1.5 text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'completed'
                      ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50',
                  ]"
                >
                  Concluídas
                </button>
                <button
                  @click="
                    filter = 'today';
                    applyFilter();
                  "
                  :class="[
                    'px-3 py-1.5 text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'today'
                      ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50',
                  ]"
                >
                  Para hoje
                </button>
                <button
                  @click="
                    filter = 'week';
                    applyFilter();
                  "
                  :class="[
                    'px-3 py-1.5 text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'week'
                      ? 'bg-indigo-100 text-indigo-700 border border-indigo-200'
                      : 'bg-white text-gray-700 border border-gray-200 hover:bg-gray-50',
                  ]"
                >
                  Esta semana
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de revisões -->
        <div
          class="overflow-hidden bg-white shadow-md rounded-lg border border-gray-100"
        >
          <div class="border-b border-gray-100 bg-white px-6 py-4">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
              <i class="ri-time-line mr-2 text-indigo-600"></i>
              Suas Revisões
              <span class="ml-2 text-sm text-gray-500">
                ({{ getFilterLabel() }})
              </span>
            </h3>
          </div>
          <div class="p-6">
            <div v-if="filteredRevisions.length === 0" class="text-center py-8">
              <div class="mx-auto h-24 w-24 text-indigo-300 mb-4">
                <i class="ri-time-line text-8xl"></i>
              </div>
              <h3 class="text-lg font-medium text-gray-900 mb-2">
                Nenhuma revisão encontrada
              </h3>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">
                Não há revisões disponíveis com os filtros selecionados.
              </p>
            </div>

            <div v-else>
              <ul class="divide-y divide-gray-100">
                <li
                  v-for="revision in filteredRevisions"
                  :key="revision.id"
                  class="py-5 hover:bg-gray-50 rounded-lg transition-colors duration-150"
                >
                  <div
                    class="flex flex-col sm:flex-row sm:items-center justify-between"
                  >
                    <div class="mb-4 sm:mb-0 sm:pr-4">
                      <div class="flex items-center">
                        <h4 class="text-lg font-medium text-gray-900">
                          {{ revision.lesson.title }}
                        </h4>
                        <span
                          :class="{
                            'bg-green-100 text-green-800':
                              revision.is_completed,
                            'bg-red-100 text-red-800': !revision.is_completed,
                          }"
                          class="ml-3 rounded-full px-2.5 py-0.5 text-xs font-medium"
                        >
                          {{ revision.is_completed ? "Concluída" : "Pendente" }}
                        </span>
                      </div>
                      <div class="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-2">
                        <div class="flex items-center text-sm text-gray-500">
                          <i
                            class="ri-book-open-line flex-shrink-0 mr-1.5 text-indigo-500"
                          ></i>
                          <span>
                            {{
                              revision.lesson.discipline
                                ? revision.lesson.discipline.name
                                : "Não especificada"
                            }}
                          </span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                          <i
                            class="ri-calendar-line flex-shrink-0 mr-1.5 text-indigo-500"
                          ></i>
                          <span>{{ formatDate(revision.scheduled_date) }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                          <i
                            class="ri-flashlight-line flex-shrink-0 mr-1.5 text-indigo-500"
                          ></i>
                          <span>{{
                            getRevisionTypeLabel(revision.revision_type)
                          }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex-shrink-0">
                      <Link
                        :href="route('revisions.show', revision.id)"
                        :class="[
                          'inline-flex items-center rounded-md px-4 py-2 text-sm font-medium transition-colors duration-150',
                          revision.is_completed
                            ? 'bg-indigo-50 text-indigo-700 hover:bg-indigo-100'
                            : 'bg-indigo-600 text-white hover:bg-indigo-700',
                        ]"
                      >
                        <i class="ri-check-line mr-1"></i>
                        {{
                          revision.is_completed
                            ? "Ver Detalhes"
                            : "Realizar Revisão"
                        }}
                      </Link>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  revisions: Array,
});

const filter = ref("all");
const filteredRevisions = ref([...props.revisions]);

const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

const getRevisionTypeLabel = (type) => {
  const labels = {
    first: "1ª Revisão",
    second: "2ª Revisão",
    third: "3ª Revisão",
    fourth: "4ª Revisão",
    recurring: "Revisão Recorrente",
    weekly: "Revisão Semanal",
  };

  return labels[type] || "Revisão";
};

const getFilterLabel = () => {
  const labels = {
    all: "Todas as revisões",
    pending: "Revisões pendentes",
    completed: "Revisões concluídas",
    today: "Revisões para hoje",
    week: "Revisões desta semana",
  };

  return labels[filter.value] || "Todas as revisões";
};

const applyFilter = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const endOfWeek = new Date(today);
  endOfWeek.setDate(today.getDate() + (7 - today.getDay()));

  switch (filter.value) {
    case "all":
      filteredRevisions.value = [...props.revisions];
      break;
    case "pending":
      filteredRevisions.value = props.revisions.filter(
        (revision) => !revision.is_completed
      );
      break;
    case "completed":
      filteredRevisions.value = props.revisions.filter(
        (revision) => revision.is_completed
      );
      break;
    case "today":
      filteredRevisions.value = props.revisions.filter((revision) => {
        const revisionDate = new Date(revision.scheduled_date);
        revisionDate.setHours(0, 0, 0, 0);
        return revisionDate.getTime() === today.getTime();
      });
      break;
    case "week":
      filteredRevisions.value = props.revisions.filter((revision) => {
        const revisionDate = new Date(revision.scheduled_date);
        revisionDate.setHours(0, 0, 0, 0);
        return revisionDate >= today && revisionDate <= endOfWeek;
      });
      break;
  }
};

onMounted(() => {
  applyFilter();
});
</script>
