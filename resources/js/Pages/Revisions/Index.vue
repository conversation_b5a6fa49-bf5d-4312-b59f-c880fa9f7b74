<template>
  <Head title="Revisões" />

  <AuthenticatedLayout>
    <template #header>
      <div
        class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
      >
        <h2
          class="text-lg sm:text-xl font-semibold leading-tight text-gray-800 dark:text-white"
        >
          Revisões
        </h2>
        <Link
          :href="route('calendar.index')"
          class="inline-flex items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
        >
          <i class="ri-calendar-line mr-2 sm:mr-1"></i>
          Ver Calendário
        </Link>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Banner informativo -->
        <div
          class="mb-6 sm:mb-8 bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-lg shadow-md overflow-hidden"
        >
          <div
            class="px-4 py-4 sm:px-6 sm:py-5 md:px-8 md:py-6 flex flex-col md:flex-row items-center justify-between text-center md:text-left"
          >
            <div class="flex flex-col md:flex-row items-center mb-4 md:mb-0">
              <div
                class="flex-shrink-0 bg-white/20 rounded-full p-2 mb-3 md:mb-0 md:mr-4"
              >
                <i class="ri-time-line text-white text-2xl sm:text-3xl"></i>
              </div>
              <div>
                <h3 class="text-base sm:text-lg font-semibold text-white">
                  Revisões Espaçadas
                </h3>
                <p class="text-indigo-100 text-sm">
                  Maximize sua retenção de conhecimento com revisões programadas
                </p>
              </div>
            </div>
            <Link
              :href="route('calendar.index')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white transition-colors duration-150"
            >
              <i class="ri-calendar-line mr-1"></i>
              Ver Calendário
            </Link>
          </div>
        </div>

        <!-- Filtros -->
        <div
          class="mb-4 sm:mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 border border-gray-100 dark:border-gray-700"
        >
          <div
            class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0"
          >
            <div class="flex items-center">
              <i
                class="ri-filter-line text-indigo-500 dark:text-indigo-400 mr-2"
              ></i>
              <span
                class="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300"
                >Filtrar por:</span
              >
            </div>
            <div class="sm:ml-4 flex-grow">
              <div class="grid grid-cols-2 gap-2 sm:flex sm:flex-wrap">
                <button
                  @click="
                    filter = 'all';
                    applyFilter();
                  "
                  :class="[
                    'px-2 py-1.5 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'all'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600',
                  ]"
                >
                  Todas
                </button>
                <button
                  @click="
                    filter = 'pending';
                    applyFilter();
                  "
                  :class="[
                    'px-2 py-1.5 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'pending'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600',
                  ]"
                >
                  Pendentes
                </button>
                <button
                  @click="
                    filter = 'completed';
                    applyFilter();
                  "
                  :class="[
                    'px-2 py-1.5 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'completed'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600',
                  ]"
                >
                  Concluídas
                </button>
                <button
                  @click="
                    filter = 'today';
                    applyFilter();
                  "
                  :class="[
                    'px-2 py-1.5 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-colors duration-150',
                    filter === 'today'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600',
                  ]"
                >
                  Para hoje
                </button>
                <button
                  @click="
                    filter = 'week';
                    applyFilter();
                  "
                  :class="[
                    'px-2 py-1.5 sm:px-3 text-xs sm:text-sm font-medium rounded-md transition-colors duration-150 col-span-2 sm:col-span-1',
                    filter === 'week'
                      ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-700'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600',
                  ]"
                >
                  Esta semana
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Lista de revisões -->
        <div
          class="overflow-hidden bg-white dark:bg-gray-800 shadow-md rounded-lg border border-gray-100 dark:border-gray-700"
        >
          <div
            class="border-b border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6 sm:py-4"
          >
            <h3
              class="text-base sm:text-lg font-medium text-gray-900 dark:text-white flex flex-col sm:flex-row sm:items-center"
            >
              <span class="flex items-center">
                <i
                  class="ri-time-line mr-2 text-indigo-600 dark:text-indigo-400"
                ></i>
                Suas Revisões
              </span>
              <span
                class="mt-1 sm:mt-0 sm:ml-2 text-xs sm:text-sm text-gray-500 dark:text-gray-400"
              >
                ({{ getFilterLabel() }})
              </span>
            </h3>
          </div>
          <div class="p-4 sm:p-6">
            <div
              v-if="filteredRevisions.length === 0"
              class="text-center py-6 sm:py-8"
            >
              <div
                class="mx-auto h-16 w-16 sm:h-24 sm:w-24 text-indigo-300 dark:text-indigo-600 mb-3 sm:mb-4"
              >
                <i class="ri-time-line text-6xl sm:text-8xl"></i>
              </div>
              <h3
                class="text-base sm:text-lg font-medium text-gray-900 dark:text-white mb-2"
              >
                Nenhuma revisão encontrada
              </h3>
              <p
                class="text-sm sm:text-base text-gray-500 dark:text-gray-400 mb-4 sm:mb-6 max-w-md mx-auto"
              >
                Não há revisões disponíveis com os filtros selecionados.
              </p>
            </div>

            <div v-else>
              <ul class="divide-y divide-gray-100 dark:divide-gray-700">
                <li
                  v-for="revision in filteredRevisions"
                  :key="revision.id"
                  class="py-3 sm:py-5 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors duration-150"
                >
                  <div
                    class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0"
                  >
                    <div class="sm:pr-4 min-w-0 flex-1">
                      <div
                        class="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0"
                      >
                        <h4
                          class="text-base sm:text-lg font-medium text-gray-900 dark:text-white truncate"
                        >
                          {{ revision.lesson.title }}
                        </h4>
                        <span
                          :class="{
                            'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-300':
                              revision.is_completed,
                            'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-300':
                              !revision.is_completed,
                          }"
                          class="sm:ml-3 rounded-full px-2 py-0.5 text-xs font-medium self-start sm:self-center"
                        >
                          {{ revision.is_completed ? "Concluída" : "Pendente" }}
                        </span>
                      </div>
                      <div
                        class="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-1 sm:gap-2"
                      >
                        <div
                          class="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400"
                        >
                          <i
                            class="ri-book-open-line flex-shrink-0 mr-1.5 text-indigo-500 dark:text-indigo-400"
                          ></i>
                          <span class="truncate">
                            {{
                              revision.lesson.discipline
                                ? revision.lesson.discipline.name
                                : "Não especificada"
                            }}
                          </span>
                        </div>
                        <div
                          class="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400"
                        >
                          <i
                            class="ri-calendar-line flex-shrink-0 mr-1.5 text-indigo-500 dark:text-indigo-400"
                          ></i>
                          <span>{{ formatDate(revision.scheduled_date) }}</span>
                        </div>
                        <div
                          class="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400"
                        >
                          <i
                            class="ri-flashlight-line flex-shrink-0 mr-1.5 text-indigo-500 dark:text-indigo-400"
                          ></i>
                          <span class="truncate">{{
                            getRevisionTypeLabel(revision.revision_type)
                          }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="flex-shrink-0">
                      <Link
                        :href="route('revisions.show', revision.id)"
                        :class="[
                          'inline-flex items-center justify-center rounded-md px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium transition-colors duration-150 w-full sm:w-auto',
                          revision.is_completed
                            ? 'bg-indigo-50 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-100 dark:hover:bg-indigo-900/70'
                            : 'bg-indigo-600 text-white hover:bg-indigo-700',
                        ]"
                      >
                        <i class="ri-check-line mr-1"></i>
                        {{
                          revision.is_completed
                            ? "Ver Detalhes"
                            : "Realizar Revisão"
                        }}
                      </Link>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { Head, Link } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  revisions: Array,
});

const filter = ref("all");
const filteredRevisions = ref([...props.revisions]);

const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

const getRevisionTypeLabel = (type) => {
  const labels = {
    first: "1ª Revisão",
    second: "2ª Revisão",
    third: "3ª Revisão",
    fourth: "4ª Revisão",
    recurring: "Revisão Recorrente",
    weekly: "Revisão Semanal",
  };

  return labels[type] || "Revisão";
};

const getFilterLabel = () => {
  const labels = {
    all: "Todas as revisões",
    pending: "Revisões pendentes",
    completed: "Revisões concluídas",
    today: "Revisões para hoje",
    week: "Revisões desta semana",
  };

  return labels[filter.value] || "Todas as revisões";
};

const applyFilter = () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const endOfWeek = new Date(today);
  endOfWeek.setDate(today.getDate() + (7 - today.getDay()));

  switch (filter.value) {
    case "all":
      filteredRevisions.value = [...props.revisions];
      break;
    case "pending":
      filteredRevisions.value = props.revisions.filter(
        (revision) => !revision.is_completed
      );
      break;
    case "completed":
      filteredRevisions.value = props.revisions.filter(
        (revision) => revision.is_completed
      );
      break;
    case "today":
      filteredRevisions.value = props.revisions.filter((revision) => {
        const revisionDate = new Date(revision.scheduled_date);
        revisionDate.setHours(0, 0, 0, 0);
        return revisionDate.getTime() === today.getTime();
      });
      break;
    case "week":
      filteredRevisions.value = props.revisions.filter((revision) => {
        const revisionDate = new Date(revision.scheduled_date);
        revisionDate.setHours(0, 0, 0, 0);
        return revisionDate >= today && revisionDate <= endOfWeek;
      });
      break;
  }
};

onMounted(() => {
  applyFilter();
});
</script>
