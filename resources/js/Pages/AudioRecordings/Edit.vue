<template>
  <Head title="Editar Áudio de Revisão" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Editar Áudio: {{ audioRecording.title }}
        </h2>
        <Link
          :href="route('audio-recordings.show', audioRecording.id)"
          class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
        >
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
          <div class="p-6">
            <form @submit.prevent="submit">
              <div class="mb-6">
                <InputLabel for="title" value="Título do Áudio" />
                <TextInput
                  id="title"
                  type="text"
                  class="mt-1 block w-full"
                  v-model="form.title"
                  required
                  autofocus
                />
                <InputError class="mt-2" :message="form.errors.title" />
              </div>

              <div class="mb-6">
                <InputLabel value="Áudio Atual" />
                <div class="mt-1">
                  <audio controls class="w-full" :src="audioUrl"></audio>
                </div>
              </div>

              <div class="mb-6">
                <InputLabel for="audio_file" value="Substituir Áudio (opcional)" />
                <input
                  id="audio_file"
                  type="file"
                  class="mt-1 block w-full rounded-md border border-gray-300 p-2 text-sm"
                  accept="audio/*"
                  @change="handleFileChange"
                />
                <p class="mt-1 text-xs text-gray-500">
                  Formatos aceitos: MP3, WAV, OGG (máx. 20MB)
                </p>
                <InputError class="mt-2" :message="form.errors.audio_file" />
              </div>

              <div class="flex items-center justify-end">
                <PrimaryButton
                  class="ml-4"
                  :class="{ 'opacity-25': form.processing }"
                  :disabled="form.processing"
                >
                  Atualizar Áudio
                </PrimaryButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link, useForm } from '@inertiajs/vue3';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextInput from '@/Components/TextInput.vue';

const props = defineProps({
  audioRecording: Object,
  audioUrl: String,
});

const form = useForm({
  title: props.audioRecording.title,
  audio_file: null,
  _method: 'PUT',
});

const handleFileChange = (e) => {
  if (e.target.files.length > 0) {
    form.audio_file = e.target.files[0];
  }
};

const submit = () => {
  form.post(route('audio-recordings.update', props.audioRecording.id));
};
</script>
