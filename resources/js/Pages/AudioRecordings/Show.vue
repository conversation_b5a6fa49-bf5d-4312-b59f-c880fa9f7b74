<template>
  <Head title="Áudio de Revisão" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800">
          Áudio: {{ audioRecording.title }}
        </h2>
        <Link
          :href="route('lessons.show', audioRecording.lesson.id)"
          class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
        >
          Voltar
        </Link>
      </div>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="overflow-hidden bg-white shadow-sm sm:rounded-lg">
          <div class="p-6">
            <div class="mb-6">
              <h3 class="text-lg font-medium text-gray-900">
                {{ audioRecording.title }}
              </h3>
              <p class="mt-1 text-sm text-gray-500">
                Aula: {{ audioRecording.lesson.title }}
              </p>
              <p
                v-if="audioRecording.duration_seconds"
                class="mt-1 text-sm text-gray-500"
              >
                Duração: {{ formatDuration(audioRecording.duration_seconds) }}
              </p>
            </div>

            <div class="mt-6">
              <!-- Player de áudio personalizado -->
              <div
                class="custom-audio-player rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
              >
                <div class="flex items-center space-x-4">
                  <!-- Botão de play/pause -->
                  <button
                    @click="togglePlay"
                    class="play-button flex h-12 w-12 items-center justify-center rounded-full bg-indigo-600 text-white shadow-md transition-all duration-200 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    <i v-if="!isPlaying" class="ri-play-fill text-2xl"></i>
                    <i v-else class="ri-pause-fill text-2xl"></i>
                  </button>

                  <div class="flex-grow">
                    <!-- Barra de progresso -->
                    <div
                      class="relative h-2.5 overflow-hidden rounded-full bg-gray-200"
                    >
                      <div
                        class="absolute top-0 left-0 h-full rounded-full bg-indigo-600 transition-all duration-100"
                        :style="{
                          width: `${audioProgress}%`,
                        }"
                      ></div>
                    </div>

                    <!-- Tempo atual / Duração total -->
                    <div
                      class="mt-1.5 flex justify-between text-xs text-gray-500"
                    >
                      <span>{{ formatTime(audioCurrentTime) }}</span>
                      <span>{{
                        formatDuration(audioRecording.duration_seconds)
                      }}</span>
                    </div>
                  </div>

                  <!-- Controles de áudio -->
                  <div class="audio-controls flex items-center space-x-4">
                    <!-- Controle de velocidade -->
                    <div class="playback-speed-control relative">
                      <button
                        @click="toggleSpeedMenu"
                        class="flex items-center text-xs font-medium text-gray-600 hover:text-gray-800 focus:outline-none"
                      >
                        <i class="ri-speed-line mr-1"></i>
                        {{ playbackSpeed }}x
                      </button>

                      <!-- Menu de velocidade -->
                      <div
                        v-if="speedMenuOpen"
                        class="absolute bottom-full left-0 mb-2 w-24 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 z-10"
                      >
                        <div
                          class="py-1"
                          role="menu"
                          aria-orientation="vertical"
                        >
                          <button
                            v-for="speed in [
                              0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0,
                            ]"
                            :key="speed"
                            @click="changePlaybackSpeed(speed)"
                            class="block w-full px-4 py-2 text-left text-xs text-gray-700 hover:bg-gray-100"
                            :class="{
                              'font-bold text-indigo-600':
                                playbackSpeed === speed,
                            }"
                            role="menuitem"
                          >
                            {{ speed }}x
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Controle de volume -->
                    <div class="volume-control flex items-center space-x-2">
                      <button
                        @click="toggleMute"
                        class="text-gray-600 hover:text-gray-800 focus:outline-none"
                      >
                        <i
                          v-if="!isMuted"
                          class="ri-volume-up-line text-xl"
                        ></i>
                        <i v-else class="ri-volume-mute-line text-xl"></i>
                      </button>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        :value="audioVolume"
                        @input="updateVolume"
                        class="h-1.5 w-20 cursor-pointer appearance-none rounded-lg bg-gray-300"
                      />
                    </div>
                  </div>
                </div>

                <!-- Elemento de áudio oculto para controle via JavaScript -->
                <audio
                  ref="audioElement"
                  :src="audioUrl"
                  preload="metadata"
                  @timeupdate="updateProgress"
                  @ended="audioEnded"
                  class="hidden"
                ></audio>
              </div>
            </div>

            <div class="mt-8">
              <h4 class="text-md font-medium text-gray-900">
                Informações da Aula
              </h4>
              <div class="mt-2 rounded-md bg-gray-50 p-4">
                <p class="text-sm text-gray-700">
                  <strong>Disciplina:</strong>
                  {{ audioRecording.lesson.discipline.name }}
                </p>
                <p class="mt-2 text-sm text-gray-700">
                  <strong>Data de Estudo:</strong>
                  {{ formatDate(audioRecording.lesson.study_date) }}
                </p>
                <p
                  v-if="audioRecording.lesson.description"
                  class="mt-2 text-sm text-gray-700"
                >
                  <strong>Descrição:</strong>
                  {{ audioRecording.lesson.description }}
                </p>
              </div>
            </div>

            <div class="mt-8 flex justify-end space-x-3">
              <Link
                :href="route('lessons.show', audioRecording.lesson.id)"
                class="rounded-md bg-gray-200 px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-300"
              >
                Voltar para Aula
              </Link>
              <button
                @click="confirmDelete"
                class="rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700"
              >
                Excluir Áudio
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <Modal :show="showDeleteModal" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900">Confirmar Exclusão</h2>
        <p class="mt-1 text-sm text-gray-600">
          Tem certeza que deseja excluir o áudio "{{ audioRecording.title }}"?
          Esta ação não pode ser desfeita.
        </p>
        <div class="mt-6 flex justify-end space-x-3">
          <SecondaryButton @click="closeModal">Cancelar</SecondaryButton>
          <DangerButton
            @click="deleteAudio"
            :class="{ 'opacity-25': processing }"
            :disabled="processing"
          >
            Excluir Áudio
          </DangerButton>
        </div>
      </div>
    </Modal>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import { Head, Link, router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import DangerButton from "@/Components/DangerButton.vue";

const props = defineProps({
  audioRecording: Object,
  audioUrl: String,
});

const showDeleteModal = ref(false);
const processing = ref(false);

// Player de áudio personalizado
const audioElement = ref(null);
const isPlaying = ref(false);
const isMuted = ref(false);
const audioProgress = ref(0);
const audioCurrentTime = ref(0);
const audioVolume = ref(100);
const playbackSpeed = ref(1.0);
const speedMenuOpen = ref(false);

// Formatar duração do áudio (mm:ss)
const formatDuration = (seconds) => {
  if (!seconds) return "00:00";
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

// Formatar tempo atual do áudio (mm:ss)
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

// Formatar data
const formatDate = (dateString) => {
  const options = { year: "numeric", month: "long", day: "numeric" };
  return new Date(dateString).toLocaleDateString("pt-BR", options);
};

// Alternar reprodução (play/pause)
const togglePlay = () => {
  if (!audioElement.value) return;

  if (isPlaying.value) {
    audioElement.value.pause();
  } else {
    audioElement.value.play();
  }

  isPlaying.value = !isPlaying.value;
};

// Atualizar progresso do áudio
const updateProgress = () => {
  if (!audioElement.value) return;

  const duration = audioElement.value.duration;
  if (duration > 0) {
    audioCurrentTime.value = audioElement.value.currentTime;
    audioProgress.value = (audioElement.value.currentTime / duration) * 100;
  }
};

// Quando o áudio terminar
const audioEnded = () => {
  isPlaying.value = false;
  audioProgress.value = 0;
  audioCurrentTime.value = 0;
};

// Alternar mudo
const toggleMute = () => {
  if (!audioElement.value) return;

  audioElement.value.muted = !audioElement.value.muted;
  isMuted.value = audioElement.value.muted;
};

// Atualizar volume
const updateVolume = (event) => {
  if (!audioElement.value) return;

  const volume = event.target.value / 100;
  audioElement.value.volume = volume;
  audioVolume.value = event.target.value;

  // Se o volume for 0, considerar como mudo
  if (volume === 0) {
    isMuted.value = true;
  } else if (isMuted.value) {
    isMuted.value = false;
    audioElement.value.muted = false;
  }
};

// Alternar menu de velocidade
const toggleSpeedMenu = () => {
  speedMenuOpen.value = !speedMenuOpen.value;

  // Adicionar um event listener para fechar o menu quando clicar fora dele
  if (speedMenuOpen.value) {
    setTimeout(() => {
      const closeMenu = (e) => {
        const speedMenu = document.querySelector(".playback-speed-control");
        if (speedMenu && !speedMenu.contains(e.target)) {
          speedMenuOpen.value = false;
          document.removeEventListener("click", closeMenu);
        }
      };

      document.addEventListener("click", closeMenu);
    }, 0);
  }
};

// Alterar velocidade de reprodução
const changePlaybackSpeed = (speed) => {
  if (!audioElement.value) return;

  audioElement.value.playbackRate = speed;
  playbackSpeed.value = speed;
  speedMenuOpen.value = false;
};

// Confirmação de exclusão
const confirmDelete = () => {
  showDeleteModal.value = true;
};

const closeModal = () => {
  showDeleteModal.value = false;
};

const deleteAudio = () => {
  processing.value = true;

  router.delete(route("audio-recordings.destroy", props.audioRecording.id), {
    onSuccess: () => {
      router.visit(route("lessons.show", props.audioRecording.lesson.id));
    },
    onError: () => {
      processing.value = false;
    },
  });
};

// Adicionar event listeners para teclas de atalho
const handleKeydown = (e) => {
  if (
    e.code === "Space" &&
    e.target.tagName !== "INPUT" &&
    e.target.tagName !== "TEXTAREA"
  ) {
    e.preventDefault();
    togglePlay();
  }
};

onMounted(() => {
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
/* Estilos para o player de áudio personalizado */
.custom-audio-player {
  transition: all 0.3s ease;
}

.play-button {
  transition: all 0.2s ease;
}

.play-button:active {
  transform: scale(0.95);
}

/* Estilos para o controle de volume */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 5px;
  background: #e5e7eb;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  background: #6366f1;
  transform: scale(1.1);
}

input[type="range"]::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border: none;
  border-radius: 50%;
  background: #4f46e5;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  background: #6366f1;
}

/* Estilos para o controle de velocidade */
.playback-speed-control button {
  transition: all 0.2s ease;
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
}

.playback-speed-control button:hover {
  background-color: #f3f4f6;
}
</style>
