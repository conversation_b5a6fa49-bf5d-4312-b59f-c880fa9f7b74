<template>
  <Head title="Revisão Geral - Estudus" />

  <AuthenticatedLayout>
    <template #header>
      <div class="flex items-center justify-between">
        <h2 class="text-xl font-semibold leading-tight text-gray-800 dark:text-gray-200">
          <i class="ri-refresh-line mr-2"></i>
          Revis<PERSON> Geral
        </h2>
      </div>
    </template>

    <div class="py-6 sm:py-12">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <!-- Introduction -->
        <div class="mb-8 rounded-lg bg-gradient-to-r from-indigo-50 to-blue-50 p-6 dark:from-indigo-900/20 dark:to-blue-900/20">
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="flex h-12 w-12 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900/50">
                <i class="ri-book-read-line text-2xl text-indigo-600 dark:text-indigo-400"></i>
              </div>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                Revisão Geral de Conteúdo
              </h3>
              <p class="mt-2 text-gray-600 dark:text-gray-300">
                Selecione um grupo e uma disciplina para acessar rapidamente todas as aulas e revisar o conteúdo estudado. 
                Ideal para preparação antes de provas e avaliações.
              </p>
            </div>
          </div>
        </div>

        <!-- Filters -->
        <div class="mb-8 rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
          <h3 class="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            <i class="ri-filter-line mr-2"></i>
            Selecionar Conteúdo para Revisão
          </h3>
          
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- Group Selection -->
            <div>
              <label for="group" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Grupo de Disciplinas
              </label>
              <select
                id="group"
                v-model="selectedGroupId"
                @change="onGroupChange"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Selecione um grupo</option>
                <option
                  v-for="group in disciplineGroups"
                  :key="group.id"
                  :value="group.id"
                >
                  {{ group.name }}
                </option>
              </select>
            </div>

            <!-- Discipline Selection -->
            <div>
              <label for="discipline" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Disciplina
              </label>
              <select
                id="discipline"
                v-model="selectedDisciplineId"
                @change="onDisciplineChange"
                :disabled="!selectedGroupId"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 disabled:opacity-50 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Selecione uma disciplina</option>
                <option
                  v-for="discipline in filteredDisciplines"
                  :key="discipline.id"
                  :value="discipline.id"
                >
                  {{ discipline.name }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- Lessons List -->
        <div v-if="lessons.length > 0" class="space-y-6">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              <i class="ri-list-check-2 mr-2"></i>
              Aulas Disponíveis para Revisão
              <span class="ml-2 text-sm text-gray-500">({{ lessons.length }} aulas)</span>
            </h3>
          </div>

          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div
              v-for="lesson in lessons"
              :key="lesson.id"
              class="group rounded-lg bg-white p-6 shadow-md transition duration-200 hover:shadow-lg dark:bg-gray-800"
            >
              <!-- Lesson Header -->
              <div class="mb-4">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                  {{ lesson.title }}
                </h4>
                <p v-if="lesson.description" class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                  {{ lesson.description }}
                </p>
                <p class="mt-2 text-xs text-gray-500">
                  <i class="ri-calendar-line mr-1"></i>
                  {{ lesson.formatted_date }}
                </p>
              </div>

              <!-- Content Indicators -->
              <div class="mb-4 flex flex-wrap gap-2">
                <span
                  v-if="lesson.has_mind_map"
                  class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                >
                  <i class="ri-mind-map mr-1"></i>
                  Mapa Mental
                </span>
                <span
                  v-if="lesson.has_audio"
                  class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900/30 dark:text-green-300"
                >
                  <i class="ri-mic-fill mr-1"></i>
                  Áudio
                </span>
                <span
                  v-if="lesson.has_notes"
                  class="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                >
                  <i class="ri-sticky-note-line mr-1"></i>
                  Anotações
                </span>
                <span
                  v-if="lesson.has_flashcards"
                  class="inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 text-xs font-medium text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"
                >
                  <i class="ri-stack-line mr-1"></i>
                  FlashCards
                </span>
                <span
                  v-if="lesson.has_questions"
                  class="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900/30 dark:text-red-300"
                >
                  <i class="ri-question-line mr-1"></i>
                  Questões
                </span>
                <span
                  v-if="lesson.has_question_links"
                  class="inline-flex items-center rounded-full bg-indigo-100 px-2.5 py-0.5 text-xs font-medium text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300"
                >
                  <i class="ri-external-link-line mr-1"></i>
                  Links
                </span>
              </div>

              <!-- Review Button -->
              <Link
                :href="route('lessons.show', lesson.id)"
                class="inline-flex w-full items-center justify-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm transition duration-200 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
              >
                <i class="ri-eye-line mr-2"></i>
                Revisar Aula
              </Link>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div
          v-else-if="selectedGroupId && selectedDisciplineId"
          class="rounded-lg bg-gray-50 p-12 text-center dark:bg-gray-800/50"
        >
          <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700">
            <i class="ri-book-line text-2xl text-gray-400"></i>
          </div>
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">
            Nenhuma aula encontrada
          </h3>
          <p class="mt-2 text-gray-600 dark:text-gray-400">
            Esta disciplina ainda não possui aulas cadastradas.
          </p>
          <Link
            :href="route('lessons.create', { discipline_id: selectedDisciplineId })"
            class="mt-4 inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700"
          >
            <i class="ri-add-line mr-2"></i>
            Criar Primeira Aula
          </Link>
        </div>

        <!-- Selection State -->
        <div
          v-else
          class="rounded-lg bg-gray-50 p-12 text-center dark:bg-gray-800/50"
        >
          <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100 dark:bg-indigo-900/50">
            <i class="ri-filter-line text-2xl text-indigo-600 dark:text-indigo-400"></i>
          </div>
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">
            Selecione um grupo e disciplina
          </h3>
          <p class="mt-2 text-gray-600 dark:text-gray-400">
            Escolha um grupo de disciplinas e uma disciplina específica para ver todas as aulas disponíveis para revisão.
          </p>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { Head, Link } from "@inertiajs/vue3";
import { ref, computed, watch } from "vue";
import { router } from "@inertiajs/vue3";
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";

const props = defineProps({
  disciplineGroups: Array,
  selectedGroup: Object,
  selectedDiscipline: Object,
  lessons: Array,
  filters: Object,
});

const selectedGroupId = ref(props.filters.group_id || "");
const selectedDisciplineId = ref(props.filters.discipline_id || "");

// Computed property to filter disciplines based on selected group
const filteredDisciplines = computed(() => {
  if (!selectedGroupId.value) return [];
  
  const group = props.disciplineGroups.find(
    (group) => group.id == selectedGroupId.value
  );
  
  return group ? group.disciplines : [];
});

// Reset discipline when group changes
watch(selectedGroupId, () => {
  selectedDisciplineId.value = "";
});

const onGroupChange = () => {
  selectedDisciplineId.value = "";
  updateFilters();
};

const onDisciplineChange = () => {
  updateFilters();
};

const updateFilters = () => {
  const params = {};
  
  if (selectedGroupId.value) {
    params.group_id = selectedGroupId.value;
  }
  
  if (selectedDisciplineId.value) {
    params.discipline_id = selectedDisciplineId.value;
  }
  
  router.get(route("general-review.index"), params, {
    preserveState: true,
    preserveScroll: true,
  });
};
</script>
