<script setup>
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import DeleteUserForm from "./Partials/DeleteUserForm.vue";
import UpdatePasswordForm from "./Partials/UpdatePasswordForm.vue";
import UpdateProfileInformationForm from "./Partials/UpdateProfileInformationForm.vue";
import { Head } from "@inertiajs/vue3";

defineProps({
  mustVerifyEmail: {
    type: Boolean,
  },
  status: {
    type: String,
  },
});
</script>

<template>
  <Head title="Perfil" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="text-xl font-semibold leading-tight dark:text-white">
        Editar informações do perfil
      </h2>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl space-y-8 px-4 sm:px-6 lg:px-8">
        <!-- Informações do Perfil -->
        <div
          class="overflow-hidden rounded-xl bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
            <div class="flex items-center">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
              >
                <i class="ri-user-line text-xl"></i>
              </div>
              <h3
                class="ml-3 text-lg font-semibold text-indigo-600 dark:text-indigo-400"
              >
                Informações do Perfil
              </h3>
            </div>
          </div>
          <div class="mt-4">
            <UpdateProfileInformationForm
              :must-verify-email="mustVerifyEmail"
              :status="status"
              class="max-w-xl"
            />
          </div>
        </div>

        <!-- Atualizar Senha -->
        <div
          class="overflow-hidden rounded-xl bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
            <div class="flex items-center">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
              >
                <i class="ri-lock-line text-xl"></i>
              </div>
              <h3
                class="ml-3 text-lg font-semibold text-indigo-600 dark:text-indigo-400"
              >
                Segurança
              </h3>
            </div>
          </div>
          <div class="mt-4">
            <UpdatePasswordForm class="max-w-xl" />
          </div>
        </div>

        <!-- Excluir Conta -->
        <div
          class="overflow-hidden rounded-xl bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
            <div class="flex items-center">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400"
              >
                <i class="ri-delete-bin-line text-xl"></i>
              </div>
              <h3
                class="ml-3 text-lg font-semibold text-red-600 dark:text-red-400"
              >
                Zona de Perigo
              </h3>
            </div>
          </div>
          <div class="mt-4">
            <DeleteUserForm class="max-w-xl" />
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>
