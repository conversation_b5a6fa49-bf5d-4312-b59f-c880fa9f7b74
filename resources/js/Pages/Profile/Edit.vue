<script setup>
import AuthenticatedLayout from "@/Layouts/AuthenticatedLayout.vue";
import DeleteUserForm from "./Partials/DeleteUserForm.vue";
import UpdatePasswordForm from "./Partials/UpdatePasswordForm.vue";
import UpdateProfileInformationForm from "./Partials/UpdateProfileInformationForm.vue";
import { Head } from "@inertiajs/vue3";

defineProps({
  mustVerifyEmail: {
    type: Boolean,
  },
  status: {
    type: String,
  },
});
</script>

<template>
  <Head title="Perfil" />

  <AuthenticatedLayout>
    <template #header>
      <h2 class="text-xl font-semibold leading-tight dark:text-white">
        Editar informações do perfil
      </h2>
    </template>

    <div class="py-12">
      <div class="mx-auto max-w-7xl space-y-8 px-4 sm:px-6 lg:px-8">
        <!-- Informações do Perfil -->
        <div
          class="overflow-hidden rounded-xl bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
            <div class="flex items-center">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-5 w-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
                  />
                </svg>
              </div>
              <h3
                class="ml-3 text-lg font-semibold text-indigo-600 dark:text-indigo-400"
              >
                Informações do Perfil
              </h3>
            </div>
          </div>
          <div class="mt-4">
            <UpdateProfileInformationForm
              :must-verify-email="mustVerifyEmail"
              :status="status"
              class="max-w-xl"
            />
          </div>
        </div>

        <!-- Atualizar Senha -->
        <div
          class="overflow-hidden rounded-xl bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
            <div class="flex items-center">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-5 w-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z"
                  />
                </svg>
              </div>
              <h3
                class="ml-3 text-lg font-semibold text-indigo-600 dark:text-indigo-400"
              >
                Segurança
              </h3>
            </div>
          </div>
          <div class="mt-4">
            <UpdatePasswordForm class="max-w-xl" />
          </div>
        </div>

        <!-- Excluir Conta -->
        <div
          class="overflow-hidden rounded-xl bg-white p-6 shadow-md transition-all duration-200 hover:shadow-lg dark:bg-zinc-900 dark:ring-1 dark:ring-white/10"
        >
          <div class="border-b border-gray-200 pb-4 dark:border-gray-700">
            <div class="flex items-center">
              <div
                class="flex h-10 w-10 items-center justify-center rounded-full bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  class="h-5 w-5"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                  />
                </svg>
              </div>
              <h3
                class="ml-3 text-lg font-semibold text-red-600 dark:text-red-400"
              >
                Zona de Perigo
              </h3>
            </div>
          </div>
          <div class="mt-4">
            <DeleteUserForm class="max-w-xl" />
          </div>
        </div>
      </div>
    </div>
  </AuthenticatedLayout>
</template>
