<script setup>
import DangerButton from "@/Components/DangerButton.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import Modal from "@/Components/Modal.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import { useForm } from "@inertiajs/vue3";
import { nextTick, ref } from "vue";

const confirmingUserDeletion = ref(false);
const passwordInput = ref(null);

const form = useForm({
  password: "",
});

const confirmUserDeletion = () => {
  confirmingUserDeletion.value = true;

  nextTick(() => passwordInput.value.focus());
};

const deleteUser = () => {
  form.delete(route("profile.destroy"), {
    preserveScroll: true,
    onSuccess: () => closeModal(),
    onError: () => passwordInput.value.focus(),
    onFinish: () => form.reset(),
  });
};

const closeModal = () => {
  confirmingUserDeletion.value = false;

  form.clearErrors();
  form.reset();
};
</script>

<template>
  <section class="space-y-6">
    <p class="mb-6 text-sm text-gray-600 dark:text-gray-400">
      Quando sua conta for excluída, todos os seus recursos e dados serão
      permanentemente removidos. Antes de excluir sua conta, baixe todos os
      dados ou informações que deseja manter.
    </p>

    <DangerButton
      @click="confirmUserDeletion"
      class="bg-red-600 hover:bg-red-700 focus:ring-red-500 dark:bg-red-700 dark:hover:bg-red-800"
    >
      Excluir Conta
    </DangerButton>

    <Modal :show="confirmingUserDeletion" @close="closeModal">
      <div class="p-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white">
          Tem certeza que deseja excluir sua conta?
        </h2>

        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Quando sua conta for excluída, todos os seus recursos e dados serão
          permanentemente removidos. Digite sua senha para confirmar que deseja
          excluir permanentemente sua conta.
        </p>

        <div class="mt-6">
          <InputLabel
            for="password"
            value="Senha"
            class="text-gray-700 dark:text-gray-300"
          />

          <TextInput
            id="password"
            ref="passwordInput"
            v-model="form.password"
            type="password"
            class="mt-1 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
            placeholder="Digite sua senha"
            @keyup.enter="deleteUser"
          />

          <InputError :message="form.errors.password" class="mt-2" />
        </div>

        <div class="mt-6 flex justify-end">
          <SecondaryButton
            @click="closeModal"
            class="border-gray-300 bg-white text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
          >
            Cancelar
          </SecondaryButton>

          <DangerButton
            class="ms-3 bg-red-600 hover:bg-red-700 focus:ring-red-500 dark:bg-red-700 dark:hover:bg-red-800"
            :class="{ 'opacity-25': form.processing }"
            :disabled="form.processing"
            @click="deleteUser"
          >
            Excluir Conta
          </DangerButton>
        </div>
      </div>
    </Modal>
  </section>
</template>
