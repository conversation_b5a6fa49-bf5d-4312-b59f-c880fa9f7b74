<script setup>
import GuestLayout from "@/Layouts/GuestLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import { Head, Link, useForm } from "@inertiajs/vue3";

defineProps({
  status: {
    type: String,
  },
});

const form = useForm({
  email: "",
});

const submit = () => {
  form.post(route("password.email"));
};
</script>

<template>
  <GuestLayout>
    <Head title="Recuperar Senha" />

    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
        Recuperação de Senha
      </h2>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Vamos ajudar você a recuperar sua conta
      </p>
    </div>

    <div
      class="mb-6 rounded-lg bg-blue-50 p-4 text-sm text-gray-600 dark:bg-blue-900/30 dark:text-gray-300"
    >
      Esqueceu sua senha? Sem problemas. Informe seu endereço de e-mail e
      enviaremos um link para redefinição de senha que permitirá que você
      escolha uma nova.
    </div>

    <div
      v-if="status"
      class="mb-6 rounded-lg bg-green-50 p-4 text-sm font-medium text-green-600 dark:bg-green-900/30 dark:text-green-400"
    >
      {{ status }}
    </div>

    <form @submit.prevent="submit">
      <div>
        <InputLabel
          for="email"
          value="Email"
          class="text-gray-700 dark:text-gray-300"
        />

        <TextInput
          id="email"
          type="email"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
          v-model="form.email"
          required
          autofocus
          autocomplete="username"
        />

        <InputError class="mt-2" :message="form.errors.email" />
      </div>

      <div class="mt-6">
        <PrimaryButton
          class="w-full justify-center"
          :class="{ 'opacity-25': form.processing }"
          :disabled="form.processing"
        >
          Enviar Link de Recuperação
        </PrimaryButton>
      </div>

      <div class="mt-6 flex items-center justify-center">
        <Link
          :href="route('login')"
          class="text-sm font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
        >
          Voltar para o login
        </Link>
      </div>
    </form>
  </GuestLayout>
</template>
