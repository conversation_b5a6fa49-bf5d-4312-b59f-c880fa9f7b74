<script setup>
import Checkbox from "@/Components/Checkbox.vue";
import GuestLayout from "@/Layouts/GuestLayout.vue";
import InputError from "@/Components/InputError.vue";
import InputLabel from "@/Components/InputLabel.vue";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import TextInput from "@/Components/TextInput.vue";
import { Head, Link, useForm } from "@inertiajs/vue3";

defineProps({
  canResetPassword: {
    type: Boolean,
  },
  status: {
    type: String,
  },
});

const form = useForm({
  email: "",
  password: "",
  remember: false,
});

const submit = () => {
  form.post(route("login"), {
    onFinish: () => form.reset("password"),
  });
};
</script>

<template>
  <GuestLayout>
    <Head title="Entrar" />

    <div class="mb-6 text-center">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
        Bem-vindo de volta
      </h2>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Entre para acessar seu sistema de revisões
      </p>
    </div>

    <div
      v-if="status"
      class="mb-4 rounded-md bg-green-50 p-4 text-sm font-medium text-green-600 dark:bg-green-900/50 dark:text-green-400"
    >
      {{ status }}
    </div>

    <form @submit.prevent="submit">
      <div>
        <InputLabel
          for="email"
          value="Email"
          class="text-gray-700 dark:text-gray-300"
        />

        <TextInput
          id="email"
          type="email"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
          v-model="form.email"
          required
          autofocus
          autocomplete="username"
        />

        <InputError class="mt-2" :message="form.errors.email" />
      </div>

      <div class="mt-4">
        <div class="flex items-center justify-between">
          <InputLabel
            for="password"
            value="Senha"
            class="text-gray-700 dark:text-gray-300"
          />
          <Link
            v-if="canResetPassword"
            :href="route('password.request')"
            class="text-xs font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
          >
            Esqueceu sua senha?
          </Link>
        </div>

        <TextInput
          id="password"
          type="password"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300"
          v-model="form.password"
          required
          autocomplete="current-password"
        />

        <InputError class="mt-2" :message="form.errors.password" />
      </div>

      <div class="mt-4 block">
        <label class="flex items-center">
          <Checkbox
            name="remember"
            v-model:checked="form.remember"
            class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500 dark:border-gray-700 dark:bg-gray-900"
          />
          <span class="ms-2 text-sm text-gray-600 dark:text-gray-400">
            Lembrar de mim
          </span>
        </label>
      </div>

      <div class="mt-6">
        <PrimaryButton
          class="w-full justify-center"
          :class="{ 'opacity-25': form.processing }"
          :disabled="form.processing"
        >
          Entrar
        </PrimaryButton>
      </div>

      <div class="mt-6 flex items-center justify-center">
        <span class="text-sm text-gray-600 dark:text-gray-400"
          >Não tem uma conta?</span
        >
        <Link
          :href="route('register')"
          class="ml-1 text-sm font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300"
        >
          Registre-se
        </Link>
      </div>
    </form>
  </GuestLayout>
</template>
