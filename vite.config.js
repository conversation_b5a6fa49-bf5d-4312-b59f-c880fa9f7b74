import { defineConfig, loadEnv } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '');

    return {
        plugins: [
            laravel({
                input: 'resources/js/app.js',
                refresh: true,
            }),
            vue({
                template: {
                    transformAssetUrls: {
                        base: null,
                        includeAbsolute: false,
                    },
                },
            }),
        ],
        define: {
            'import.meta.env.APP_VERSION': JSON.stringify(env.APP_VERSION || '1.0.0'),
            'import.meta.env.APP_NAME': JSON.stringify(env.APP_NAME || 'Estudus'),
        }
    };
});
