<?php

use App\Http\Controllers\AudioRecordingController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DisciplineController;
use App\Http\Controllers\DisciplineGroupController;
use App\Http\Controllers\FlashcardController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\MindMapController;
use App\Http\Controllers\NoteController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QuestionLinkController;
use App\Http\Controllers\RevisionController;
use App\Http\Controllers\StudySessionController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
})->name('welcome');

Route::get('/university-students', function () {
    return Inertia::render('UniversityStudents', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
    ]);
})->name('university-students');

Route::get('/exam-students', function () {
    return Inertia::render('ExamStudents', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
    ]);
})->name('exam-students');

Route::get('/professionals', function () {
    return Inertia::render('Professionals', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
    ]);
})->name('professionals');

Route::get('/privacy-policy', function () {
    return Inertia::render('PrivacyPolicy', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
    ]);
})->name('privacy.policy');

Route::get('/terms-of-service', function () {
    return Inertia::render('TermsOfService', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
    ]);
})->name('terms.service');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Discipline Groups
    Route::resource('discipline-groups', DisciplineGroupController::class);

    // Disciplines
    Route::resource('disciplines', DisciplineController::class);

    // Lessons
    Route::resource('lessons', LessonController::class);

    // Mind Maps
    Route::resource('mind-maps', MindMapController::class)->except(['index']);
    Route::post('/mind-maps/auto-save', [MindMapController::class, 'autoSave'])->name('mind-maps.auto-save');

    // Question Links
    Route::resource('question-links', QuestionLinkController::class)->except(['index', 'create', 'show']);

    // Audio Recordings
    Route::resource('audio-recordings', AudioRecordingController::class)->except(['index', 'create']);

    // Revisions
    Route::resource('revisions', RevisionController::class)->only(['index', 'show', 'update']);

    // Questions
    Route::post('/lessons/{lesson}/questions', [QuestionController::class, 'store'])->name('questions.store');
    Route::post('/lessons/{lesson}/questions/import', [QuestionController::class, 'import'])->name('questions.import');
    Route::put('/questions/{question}', [QuestionController::class, 'update'])->name('questions.update');
    Route::delete('/questions/{question}', [QuestionController::class, 'destroy'])->name('questions.destroy');
    Route::post('/questions/{question}/check-answer', [QuestionController::class, 'checkAnswer'])->name('questions.check-answer');

    // Flashcards
    Route::post('/lessons/{lesson}/flashcards', [FlashcardController::class, 'store'])->name('flashcards.store');
    Route::put('/flashcards/{flashcard}', [FlashcardController::class, 'update'])->name('flashcards.update');
    Route::delete('/flashcards/{flashcard}', [FlashcardController::class, 'destroy'])->name('flashcards.destroy');

    // Calendar
    Route::get('/calendar', [CalendarController::class, 'index'])->name('calendar.index');

    // Study Sessions
    Route::resource('study-sessions', StudySessionController::class)->only(['index', 'store', 'update', 'destroy']);
    Route::post('/study-sessions/start', [StudySessionController::class, 'start'])->name('study-sessions.start');
    Route::post('/study-sessions/{studySessionId}/end', [StudySessionController::class, 'end'])->name('study-sessions.end');

    // Notes
    Route::resource('notes', NoteController::class)->only(['store', 'update', 'destroy']);
    Route::post('/notes/auto-save', [NoteController::class, 'autoSave'])->name('notes.auto-save');

    // Profile
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/users', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('users.index');
    Route::get('/users/pending', [App\Http\Controllers\Admin\UserController::class, 'pending'])->name('users.pending');
    Route::post('/users/{user}/approve', [App\Http\Controllers\Admin\UserController::class, 'approve'])->name('users.approve');
    Route::post('/users/{user}/block', [App\Http\Controllers\Admin\UserController::class, 'block'])->name('users.block');
    Route::post('/users/{user}/unblock', [App\Http\Controllers\Admin\UserController::class, 'unblock'])->name('users.unblock');
    Route::delete('/users/{user}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('users.destroy');
});

require __DIR__.'/auth.php';
