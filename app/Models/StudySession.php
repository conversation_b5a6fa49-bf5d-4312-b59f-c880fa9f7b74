<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudySession extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'discipline_id',
        'started_at',
        'ended_at',
        'duration_seconds',
        'notes',
        'is_pomodoro',
        'study_duration_minutes',
        'break_duration_minutes',
        'completed_pomodoros',
        'is_break',
        'paused_at',
        'paused_duration_seconds',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'paused_at' => 'datetime',
        'duration_seconds' => 'integer',
        'is_pomodoro' => 'boolean',
        'is_break' => 'boolean',
        'study_duration_minutes' => 'integer',
        'break_duration_minutes' => 'integer',
        'completed_pomodoros' => 'integer',
        'paused_duration_seconds' => 'integer',
    ];

    /**
     * Get the user that owns the study session.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the discipline that owns the study session.
     */
    public function discipline(): BelongsTo
    {
        return $this->belongsTo(Discipline::class);
    }
}
