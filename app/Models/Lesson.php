<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Lesson extends Model
{
    use HasFactory;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::deleting(function ($lesson) {
            // Excluir o mapa mental associado
            $lesson->mindMap()->delete();

            // Excluir os links de questões
            $lesson->questionLinks()->delete();

            // Excluir os áudios de revisão
            $lesson->audioRecordings()->delete();

            // Excluir as revisões
            $lesson->revisions()->delete();

            // Excluir as questões
            $lesson->questions()->delete();

            // Excluir os flashcards
            $lesson->flashcards()->delete();

            // Excluir as anotações
            $lesson->notes()->delete();
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'study_date',
        'discipline_id',
        'user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'study_date' => 'date',
    ];

    /**
     * Get the discipline that owns the lesson.
     */
    public function discipline(): BelongsTo
    {
        return $this->belongsTo(Discipline::class);
    }

    /**
     * Get the user that owns the lesson.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the mind map associated with the lesson.
     */
    public function mindMap(): HasOne
    {
        return $this->hasOne(MindMap::class);
    }

    /**
     * Get the question links for the lesson.
     */
    public function questionLinks(): HasMany
    {
        return $this->hasMany(QuestionLink::class);
    }

    /**
     * Get the audio recordings for the lesson.
     */
    public function audioRecordings(): HasMany
    {
        return $this->hasMany(AudioRecording::class);
    }

    /**
     * Get the revisions for the lesson.
     */
    public function revisions(): HasMany
    {
        return $this->hasMany(Revision::class);
    }

    /**
     * Get the questions for the lesson.
     */
    public function questions(): HasMany
    {
        return $this->hasMany(Question::class);
    }

    /**
     * Get the flashcards for the lesson.
     */
    public function flashcards(): HasMany
    {
        return $this->hasMany(Flashcard::class);
    }

    /**
     * Get the notes for the lesson.
     */
    public function notes(): HasMany
    {
        return $this->hasMany(Note::class);
    }
}
