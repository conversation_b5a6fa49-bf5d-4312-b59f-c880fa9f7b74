<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Discipline extends Model
{
    use HasFactory;

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::deleting(function ($discipline) {
            // Carrega todas as aulas relacionadas com seus áudios
            $discipline->lessons()->with('audioRecordings')->get()->each(function ($lesson) {
                // Exclui os arquivos físicos de áudio
                $lesson->audioRecordings->each(function ($audio) {
                    if ($audio->file_path) {
                        \Illuminate\Support\Facades\Storage::disk('public')->delete($audio->file_path);
                    }
                });

                // A exclusão das aulas e outros recursos relacionados acontecerá automaticamente
                // devido às restrições de chave estrangeira com onDelete('cascade')
            });
        });
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'user_id',
        'discipline_group_id',
    ];

    protected $appends = ['lessons_count'];

    // retorna o numero de aulas
    public function getLessonsCountAttribute()
    {
        return $this->lessons()->count();
    }

    /**
     * Get the user that owns the discipline.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the lessons for the discipline.
     */
    public function lessons(): HasMany
    {
        return $this->hasMany(Lesson::class);
    }

    /**
     * Get the discipline group that owns the discipline.
     */
    public function disciplineGroup(): BelongsTo
    {
        return $this->belongsTo(DisciplineGroup::class);
    }

    /**
     * Get the study sessions for the discipline.
     */
    public function studySessions(): HasMany
    {
        return $this->hasMany(StudySession::class);
    }

    /**
     * Get the study session totals for the discipline.
     */
    public function studySessionTotals(): HasMany
    {
        return $this->hasMany(StudySessionTotal::class);
    }
}
