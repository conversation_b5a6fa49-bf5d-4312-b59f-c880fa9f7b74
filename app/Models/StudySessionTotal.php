<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudySessionTotal extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'discipline_id',
        'study_date',
        'total_duration_seconds',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'study_date' => 'date',
        'total_duration_seconds' => 'integer',
    ];

    /**
     * Get the user that owns the study session total.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the discipline that owns the study session total.
     */
    public function discipline(): BelongsTo
    {
        return $this->belongsTo(Discipline::class);
    }
}
