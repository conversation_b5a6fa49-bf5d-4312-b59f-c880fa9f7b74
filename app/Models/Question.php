<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Question extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'text',
        'explanation',
        'lesson_id',
    ];

    /**
     * Get the lesson that owns the question.
     */
    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the alternatives for the question.
     */
    public function alternatives(): HasMany
    {
        return $this->hasMany(Alternative::class);
    }

    /**
     * Get the correct alternative for the question.
     */
    public function correctAlternative()
    {
        return $this->alternatives()->where('is_correct', true)->first();
    }
}
