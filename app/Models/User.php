<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * Get the disciplines for the user.
     */
    public function disciplines()
    {
        return $this->hasMany(Discipline::class);
    }

    /**
     * Get the lessons for the user.
     */
    public function lessons()
    {
        return $this->hasMany(Lesson::class);
    }

    /**
     * Get the discipline groups for the user.
     */
    public function disciplineGroups()
    {
        return $this->hasMany(DisciplineGroup::class);
    }

    /**
     * Get the study sessions for the user.
     */
    public function studySessions()
    {
        return $this->hasMany(StudySession::class);
    }

    /**
     * Get the study session totals for the user.
     */
    public function studySessionTotals()
    {
        return $this->hasMany(StudySessionTotal::class);
    }

    /**
     * Get the notes for the user.
     */
    public function notes()
    {
        return $this->hasMany(Note::class);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if user is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Check if user is blocked
     */
    public function isBlocked()
    {
        return $this->status === 'blocked';
    }

    /**
     * Activate user
     */
    public function activate()
    {
        $this->update(['status' => 'active']);
    }

    /**
     * Block user
     */
    public function block()
    {
        $this->update(['status' => 'blocked']);
    }

    /**
     * Unblock user
     */
    public function unblock()
    {
        $this->update(['status' => 'active']);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
