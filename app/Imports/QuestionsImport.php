<?php

namespace App\Imports;

use App\Models\Question;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class QuestionsImport implements ToCollection, WithHeadingRow, WithValidation
{
    protected $lessonId;
    protected $importedCount = 0;
    protected $errors = [];

    public function __construct($lessonId)
    {
        $this->lessonId = $lessonId;
    }

    /**
     * @param Collection $rows
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {
            try {
                // Verificar se a linha tem os dados necessários
                if (empty($row['question']) || !isset($row['iscorrect'])) {
                    $this->errors[] = "Linha ignorada: Faltam campos obrigatórios (question ou isCorrect).";
                    continue;
                }

                // Criar a questão
                $question = Question::create([
                    'text' => $row['question'],
                    'explanation' => $row['explanation'] ?? null,
                    'lesson_id' => $this->lessonId,
                ]);

                // Encontrar todas as opções (option1, option2, etc.)
                $options = [];
                $correctIndex = null;

                foreach ($row as $key => $value) {
                    if (preg_match('/^option(\d+)$/', $key, $matches) && !empty($value)) {
                        $index = (int)$matches[1] - 1; // Converter para índice baseado em 0
                        $options[$index] = $value;
                    }
                }

                // Verificar se há pelo menos 2 opções
                if (count($options) < 2) {
                    $question->delete();
                    $this->errors[] = "Questão '{$row['question']}' ignorada: Precisa ter pelo menos 2 opções.";
                    continue;
                }

                // Reordenar as opções para garantir que não há lacunas
                ksort($options);
                $options = array_values($options);

                // Determinar qual é a alternativa correta
                $correctIndex = (int)$row['iscorrect'] - 1; // Converter para índice baseado em 0

                // Verificar se o índice correto é válido
                if ($correctIndex < 0 || $correctIndex >= count($options)) {
                    $question->delete();
                    $this->errors[] = "Questão '{$row['question']}' ignorada: Índice da alternativa correta inválido.";
                    continue;
                }

                // Criar as alternativas
                foreach ($options as $index => $optionText) {
                    $question->alternatives()->create([
                        'text' => $optionText,
                        'is_correct' => ($index === $correctIndex),
                    ]);
                }

                $this->importedCount++;
            } catch (\Exception $e) {
                $this->errors[] = "Erro ao processar linha: " . $e->getMessage();
            }
        }
    }

    /**
     * Get the validation rules that apply to the import.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'question' => 'required|string|max:1000',
            'explanation' => 'nullable|string|max:1000',
            'iscorrect' => 'required|integer|min:1',
        ];
    }

    /**
     * Get the number of imported questions.
     *
     * @return int
     */
    public function getImportedCount()
    {
        return $this->importedCount;
    }

    /**
     * Get the errors that occurred during import.
     *
     * @return array
     */
    public function getErrors()
    {
        return $this->errors;
    }
}
