<?php

namespace App\Services;

use App\Models\Lesson;
use App\Models\Revision;
use Carbon\Carbon;

class RevisionScheduler
{
    /**
     * Schedule revisions for a lesson.
     *
     * @param Lesson $lesson
     * @return void
     */
    public function scheduleRevisionsForLesson(Lesson $lesson): void
    {
        // First revision: 24 hours after lesson creation
        $this->createRevision($lesson, 'first', Carbon::parse($lesson->study_date)->addDay());

        // Second revision: 7 days after the first revision
        $this->createRevision($lesson, 'second', Carbon::parse($lesson->study_date)->addDays(8));

        // Third revision: 30 days after the second revision
        $this->createRevision($lesson, 'third', Carbon::parse($lesson->study_date)->addDays(38));

        // Fourth revision: 3 months after the third revision
        $this->createRevision($lesson, 'fourth', Carbon::parse($lesson->study_date)->addDays(38)->addMonths(3));

        // Schedule weekly revisions for the next 3 months
        $this->scheduleWeeklyRevisions($lesson);
    }

    /**
     * Create a revision.
     *
     * @param Lesson $lesson
     * @param string $type
     * @param Carbon $date
     * @return Revision
     */
    private function createRevision(Lesson $lesson, string $type, Carbon $date): Revision
    {
        return Revision::create([
            'lesson_id' => $lesson->id,
            'scheduled_date' => $date,
            'revision_type' => $type,
            'is_completed' => false,
        ]);
    }

    /**
     * Schedule weekly revisions for the next 3 months.
     *
     * @param Lesson $lesson
     * @return void
     */
    private function scheduleWeeklyRevisions(Lesson $lesson): void
    {
        $startDate = Carbon::parse($lesson->study_date);
        $endDate = Carbon::parse($lesson->study_date)->addMonths(3);

        $currentDate = $startDate->copy()->next(Carbon::SUNDAY);

        while ($currentDate->lte($endDate)) {
            $this->createRevision($lesson, 'weekly', $currentDate);
            $currentDate->addWeek();
        }
    }

    /**
     * Schedule recurring revisions every 6 months.
     *
     * @param Lesson $lesson
     * @param int $count Number of recurring revisions to schedule
     * @return void
     */
    public function scheduleRecurringRevisions(Lesson $lesson, int $count = 2): void
    {
        $lastRevision = $lesson->revisions()
            ->where('revision_type', 'fourth')
            ->first();

        if (!$lastRevision) {
            return;
        }

        $startDate = Carbon::parse($lastRevision->scheduled_date);

        for ($i = 0; $i < $count; $i++) {
            $date = $startDate->copy()->addMonths(6 * ($i + 1));
            $this->createRevision($lesson, 'recurring', $date);
        }
    }
}
