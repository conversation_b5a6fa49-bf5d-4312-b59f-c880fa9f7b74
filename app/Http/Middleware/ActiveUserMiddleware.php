<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ActiveUserMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();

            if ($user->isBlocked()) {
                Auth::logout();

                return redirect()->route('login')->with('error', 'Sua conta foi bloqueada. Entre em contato com o administrador.');
            }

            if ($user->isPending()) {
                Auth::logout();

                return redirect()->route('login')->with('error', 'Sua conta ainda não foi aprovada pelo administrador.');
            }
        }

        return $next($request);
    }
}
