<?php

namespace App\Http\Controllers;

use App\Models\Discipline;
use App\Models\DisciplineGroup;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DisciplineGroupController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $disciplineGroups = auth()->user()->disciplineGroups()->with('disciplines')->latest()->get();

        return Inertia::render('DisciplineGroups/Index', [
            'disciplineGroups' => $disciplineGroups,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('DisciplineGroups/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $disciplineGroup = auth()->user()->disciplineGroups()->create($validated);

        return redirect()->route('discipline-groups.index')
            ->with('success', 'Grupo criado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(DisciplineGroup $disciplineGroup)
    {
        $this->authorize('view', $disciplineGroup);

        $disciplineGroup->load('disciplines');

        // Get chart data for this discipline group
        $studySessionController = new \App\Http\Controllers\StudySessionController;
        $chartData = $studySessionController->getChartDataForDisciplineGroup($disciplineGroup->id);

        return Inertia::render('DisciplineGroups/Show', [
            'disciplineGroup' => $disciplineGroup,
            'disciplines' => $disciplineGroup->disciplines()->latest()->get(),
            'chartData' => $chartData,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DisciplineGroup $disciplineGroup)
    {
        $this->authorize('update', $disciplineGroup);

        return Inertia::render('DisciplineGroups/Edit', [
            'disciplineGroup' => $disciplineGroup,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DisciplineGroup $disciplineGroup)
    {
        $this->authorize('update', $disciplineGroup);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $disciplineGroup->update($validated);

        return redirect()->route('discipline-groups.show', $disciplineGroup)
            ->with('success', 'Grupo atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DisciplineGroup $disciplineGroup)
    {
        $this->authorize('delete', $disciplineGroup);

        // Check if this is the default group
        if ($disciplineGroup->is_default) {
            return back()->with('error', 'Não é possível excluir o grupo padrão.');
        }

        // Get the default group for this user
        $defaultGroup = auth()->user()->disciplineGroups()->where('is_default', true)->first();

        if (! $defaultGroup) {
            // Create a default group if none exists
            $defaultGroup = auth()->user()->disciplineGroups()->create([
                'name' => 'Geral',
                'description' => 'Grupo padrão para todas as disciplinas',
                'is_default' => true,
            ]);
        }

        // Move all disciplines from this group to the default group
        Discipline::where('discipline_group_id', $disciplineGroup->id)
            ->update(['discipline_group_id' => $defaultGroup->id]);

        // Delete the group
        $disciplineGroup->delete();

        return redirect()->route('discipline-groups.index')
            ->with('success', 'Grupo excluído com sucesso!');
    }
}
