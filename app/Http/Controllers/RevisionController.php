<?php

namespace App\Http\Controllers;

use App\Models\Revision;
use App\Services\RevisionScheduler;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RevisionController extends Controller
{
    protected $revisionScheduler;

    public function __construct(RevisionScheduler $revisionScheduler)
    {
        $this->revisionScheduler = $revisionScheduler;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $revisions = Revision::with(['lesson.discipline'])
            ->whereHas('lesson', function ($query) {
                $query->where('user_id', Auth::id());
            })
            ->where('scheduled_date', '>=', Carbon::today())
            ->orderBy('scheduled_date')
            ->get();

        return Inertia::render('Revisions/Index', [
            'revisions' => $revisions,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Revision $revision)
    {
        $this->authorize('view', $revision->lesson);

        $revision->load('lesson.discipline', 'lesson.mindMap', 'lesson.questionLinks', 'lesson.audioRecordings', 'lesson.questions.alternatives', 'lesson.flashcards', 'lesson.notes');

        // Adicionar URLs dos áudios
        if ($revision->lesson->audioRecordings) {
            $revision->lesson->audioRecordings->each(function ($audio) {
                $audio->audio_url = Storage::url($audio->file_path);
            });
        }

        return Inertia::render('Revisions/Show', [
            'revision' => $revision,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Revision $revision)
    {
        $this->authorize('view', $revision->lesson);

        $validated = $request->validate([
            'is_completed' => 'required|boolean',
            'notes' => 'nullable|string',
        ]);

        $data = [
            'is_completed' => $validated['is_completed'],
            'notes' => $validated['notes'] ?? $revision->notes,
        ];

        if ($validated['is_completed'] && ! $revision->completed_date) {
            $data['completed_date'] = Carbon::today();
        } elseif (! $validated['is_completed']) {
            $data['completed_date'] = null;
        }

        $revision->update($data);

        // If this is the fourth revision and it's completed, schedule recurring revisions
        if ($revision->revision_type === 'fourth' && $revision->is_completed) {
            $this->revisionScheduler->scheduleRecurringRevisions($revision->lesson);
        }

        return redirect()->route('revisions.show', $revision)
            ->with('success', 'Revisão atualizada com sucesso!');
    }
}
