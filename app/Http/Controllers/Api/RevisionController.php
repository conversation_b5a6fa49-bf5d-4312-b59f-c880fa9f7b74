<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Revision;
use Illuminate\Http\Request;

class RevisionController extends Controller
{
    /**
     * Get upcoming revisions for today.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function upcomingRevisions(Request $request)
    {
        $today = now()->format('Y-m-d');
        
        return Revision::with(['lesson.discipline'])
            ->whereHas('lesson', function ($query) use ($request) {
                $query->where('user_id', $request->user()->id);
            })
            ->where('scheduled_date', $today)
            ->where('is_completed', false)
            ->orderBy('scheduled_date')
            ->take(5)
            ->get();
    }
}
