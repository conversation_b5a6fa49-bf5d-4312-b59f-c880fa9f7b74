<?php

namespace App\Http\Controllers;

use App\Models\Discipline;
use App\Models\StudySession;
use App\Models\StudySessionTotal;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class StudySessionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $studySessions = Auth::user()->studySessions()
            ->with('discipline')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get all discipline groups for the current user with their disciplines
        $disciplineGroups = Auth::user()->disciplineGroups()
            ->with(['disciplines' => function ($query) {
                $query->orderBy('name');
            }])
            ->orderBy('name')
            ->get();

        // Get all disciplines for the current user (for backward compatibility)
        $disciplines = Auth::user()->disciplines()
            ->orderBy('name')
            ->get();

        // Get study session totals for chart data
        $chartData = $this->getChartData();

        return Inertia::render('StudySessions/Index', [
            'studySessions' => $studySessions,
            'disciplineGroups' => $disciplineGroups,
            'disciplines' => $disciplines,
            'chartData' => $chartData,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'discipline_id' => 'required|exists:disciplines,id',
            'started_at' => 'required|date',
            'ended_at' => 'required|date|after:started_at',
            'notes' => 'nullable|string',
        ]);

        // Check if the discipline belongs to the authenticated user
        $discipline = Discipline::findOrFail($validated['discipline_id']);
        $this->authorize('view', $discipline);

        // Calculate duration in seconds
        $startedAt = Carbon::parse($validated['started_at']);
        $endedAt = Carbon::parse($validated['ended_at']);

        // CORREÇÃO: Use startedAt->diffInSeconds(endedAt) em vez de endedAt->diffInSeconds(startedAt)
        $durationSeconds = $startedAt->diffInSeconds($endedAt);

        // Create the study session
        $studySession = Auth::user()->studySessions()->create([
            'discipline_id' => $validated['discipline_id'],
            'started_at' => $validated['started_at'],
            'ended_at' => $validated['ended_at'],
            'duration_seconds' => $durationSeconds,
            'notes' => $validated['notes'],
        ]);

        // Update or create the study session total for this date and discipline
        $studyDate = Carbon::parse($validated['started_at'])->toDateString();

        $studySessionTotal = StudySessionTotal::updateOrCreate(
            [
                'user_id' => Auth::id(),
                'discipline_id' => $validated['discipline_id'],
                'study_date' => $studyDate,
            ],
            [
                'total_duration_seconds' => \DB::raw('total_duration_seconds + '.$durationSeconds),
            ]
        );

        return redirect()->route('study-sessions.index')
            ->with('success', 'Sessão de estudo registrada com sucesso!');
    }

    /**
     * Start a new study session.
     */
    public function start(Request $request)
    {
        $validated = $request->validate([
            'discipline_id' => 'required|exists:disciplines,id',
            'is_pomodoro' => 'boolean',
            'study_duration_minutes' => 'nullable|integer|min:1|max:120',
            'break_duration_minutes' => 'nullable|integer|min:1|max:60',
        ]);

        // Check if the discipline belongs to the authenticated user
        $discipline = Discipline::findOrFail($validated['discipline_id']);
        $this->authorize('view', $discipline);

        // Create a new study session with start time
        $studySession = Auth::user()->studySessions()->create([
            'discipline_id' => $validated['discipline_id'],
            'started_at' => now(),
            'is_pomodoro' => $validated['is_pomodoro'] ?? false,
            'study_duration_minutes' => $validated['study_duration_minutes'] ?? null,
            'break_duration_minutes' => $validated['break_duration_minutes'] ?? null,
        ]);

        return response()->json([
            'success' => true,
            'study_session_id' => $studySession->id,
            'started_at' => $studySession->started_at,
            'is_pomodoro' => $studySession->is_pomodoro,
            'study_duration_minutes' => $studySession->study_duration_minutes,
            'break_duration_minutes' => $studySession->break_duration_minutes,
        ]);
    }

    /**
     * End an active study session.
     */
    public function end(Request $request, $studySessionId)
    {
        // Find the study session
        $studySession = StudySession::findOrFail($studySessionId);

        // Check if the study session belongs to the authenticated user
        if ($studySession->user_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Você não tem permissão para acessar esta sessão de estudo.',
            ], 403);
        }

        // Validate that the session hasn't already ended
        if ($studySession->ended_at) {
            return response()->json([
                'success' => false,
                'message' => 'Esta sessão de estudo já foi finalizada.',
            ], 400);
        }

        // Update the study session with end time and duration
        $endedAt = now();
        $startedAt = Carbon::parse($studySession->started_at);

        // CORREÇÃO: Use startedAt->diffInSeconds(endedAt) em vez de endedAt->diffInSeconds(startedAt)
        $durationSeconds = $startedAt->diffInSeconds($endedAt);

        $studySession->update([
            'ended_at' => $endedAt,
            'duration_seconds' => $durationSeconds,
            'notes' => $request->input('notes'),
        ]);

        // Update or create the study session total for this date and discipline
        $studyDate = Carbon::parse($studySession->started_at)->toDateString();

        // First, get the existing record if it exists
        $existingTotal = StudySessionTotal::where([
            'user_id' => Auth::id(),
            'discipline_id' => $studySession->discipline_id,
            'study_date' => $studyDate,
        ])->first();

        if ($existingTotal) {
            // Update existing record
            $existingTotal->total_duration_seconds += $durationSeconds;
            $existingTotal->save();
        } else {
            // Create new record
            StudySessionTotal::create([
                'user_id' => Auth::id(),
                'discipline_id' => $studySession->discipline_id,
                'study_date' => $studyDate,
                'total_duration_seconds' => $durationSeconds,
            ]);
        }

        return response()->json([
            'success' => true,
            'study_session' => $studySession->fresh(),
            'duration_formatted' => $this->formatDuration($durationSeconds),
        ]);
    }

    /**
     * Pause an active study session.
     */
    public function pause(Request $request, $studySessionId)
    {
        $studySession = StudySession::findOrFail($studySessionId);

        // Check if the session belongs to the authenticated user
        if ($studySession->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if session is not already paused
        if ($studySession->paused_at) {
            return response()->json(['error' => 'Session is already paused'], 400);
        }

        $studySession->update([
            'paused_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'paused_at' => $studySession->paused_at,
        ]);
    }

    /**
     * Resume a paused study session.
     */
    public function resume(Request $request, $studySessionId)
    {
        $studySession = StudySession::findOrFail($studySessionId);

        // Check if the session belongs to the authenticated user
        if ($studySession->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if session is paused
        if (! $studySession->paused_at) {
            return response()->json(['error' => 'Session is not paused'], 400);
        }

        // Calculate paused duration and add to total
        $pausedDuration = now()->diffInSeconds($studySession->paused_at);

        $studySession->update([
            'paused_at' => null,
            'paused_duration_seconds' => $studySession->paused_duration_seconds + $pausedDuration,
        ]);

        return response()->json([
            'success' => true,
            'paused_duration_added' => $pausedDuration,
            'total_paused_duration' => $studySession->paused_duration_seconds,
        ]);
    }

    /**
     * Complete a pomodoro cycle and start break.
     */
    public function completePomodoro(Request $request, $studySessionId)
    {
        $studySession = StudySession::findOrFail($studySessionId);

        // Check if the session belongs to the authenticated user
        if ($studySession->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if it's a pomodoro session
        if (! $studySession->is_pomodoro) {
            return response()->json(['error' => 'Not a pomodoro session'], 400);
        }

        $studySession->update([
            'completed_pomodoros' => $studySession->completed_pomodoros + 1,
            'is_break' => true,
        ]);

        return response()->json([
            'success' => true,
            'completed_pomodoros' => $studySession->completed_pomodoros,
            'is_break' => true,
            'break_duration_minutes' => $studySession->break_duration_minutes,
        ]);
    }

    /**
     * Complete break and return to study.
     */
    public function completeBreak(Request $request, $studySessionId)
    {
        $studySession = StudySession::findOrFail($studySessionId);

        // Check if the session belongs to the authenticated user
        if ($studySession->user_id !== Auth::id()) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check if it's a pomodoro session in break
        if (! $studySession->is_pomodoro || ! $studySession->is_break) {
            return response()->json(['error' => 'Not in break mode'], 400);
        }

        $studySession->update([
            'is_break' => false,
        ]);

        return response()->json([
            'success' => true,
            'is_break' => false,
            'study_duration_minutes' => $studySession->study_duration_minutes,
        ]);
    }

    /**
     * Get chart data for study sessions.
     */
    private function getChartData()
    {
        // Get study session totals for the last 30 days
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        $studySessionTotals = Auth::user()->studySessionTotals()
            ->with('discipline')
            ->whereBetween('study_date', [$startDate, $endDate])
            ->orderBy('study_date')
            ->get();

        // Format data for chart
        $chartData = [
            'labels' => [],
            'datasets' => [],
        ];

        // Create a map of discipline IDs to colors
        $disciplines = Auth::user()->disciplines()->get();
        $colorMap = [];
        $colors = [
            'rgba(79, 70, 229, 0.8)', // indigo-600
            'rgba(16, 185, 129, 0.8)', // emerald-500
            'rgba(245, 158, 11, 0.8)', // amber-500
            'rgba(239, 68, 68, 0.8)',  // red-500
            'rgba(59, 130, 246, 0.8)', // blue-500
            'rgba(236, 72, 153, 0.8)', // pink-500
            'rgba(139, 92, 246, 0.8)', // purple-500
        ];

        foreach ($disciplines as $index => $discipline) {
            $colorMap[$discipline->id] = $colors[$index % count($colors)];
        }

        // Group by date
        $dateData = [];
        foreach ($studySessionTotals as $total) {
            $date = $total->study_date->format('Y-m-d');
            if (! isset($dateData[$date])) {
                $dateData[$date] = [];
            }
            $dateData[$date][$total->discipline_id] = $total->total_duration_seconds / 60; // Convert to minutes
        }

        // Fill in missing dates
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $formattedDate = $currentDate->format('d/m');
            $chartData['labels'][] = $formattedDate;

            if (! isset($dateData[$dateString])) {
                $dateData[$dateString] = [];
            }

            $currentDate->addDay();
        }

        // Create datasets for each discipline
        foreach ($disciplines as $discipline) {
            $dataset = [
                'label' => $discipline->name,
                'backgroundColor' => $colorMap[$discipline->id],
                'data' => [],
            ];

            foreach ($chartData['labels'] as $index => $label) {
                $dateString = Carbon::now()->subDays(30 - $index)->format('Y-m-d');
                $dataset['data'][] = $dateData[$dateString][$discipline->id] ?? 0;
            }

            $chartData['datasets'][] = $dataset;
        }

        return $chartData;
    }

    /**
     * Format duration in seconds to a human-readable format.
     */
    private function formatDuration($seconds)
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
    }

    /**
     * Get chart data for study sessions for a specific discipline group.
     */
    public function getChartDataForDisciplineGroup($disciplineGroupId)
    {
        // Get study session totals for the last 30 days
        $startDate = Carbon::now()->subDays(30);
        $endDate = Carbon::now();

        // Get the discipline group and check if it belongs to the current user
        $disciplineGroup = Auth::user()->disciplineGroups()->findOrFail($disciplineGroupId);

        // Get all disciplines in this group
        $disciplines = $disciplineGroup->disciplines;
        $disciplineIds = $disciplines->pluck('id')->toArray();

        if (empty($disciplineIds)) {
            // Return empty chart data if there are no disciplines in this group
            return [
                'labels' => [],
                'datasets' => [],
            ];
        }

        // Get study session totals for disciplines in this group
        $studySessionTotals = StudySessionTotal::where('user_id', Auth::id())
            ->whereIn('discipline_id', $disciplineIds)
            ->whereBetween('study_date', [$startDate, $endDate])
            ->with('discipline')
            ->orderBy('study_date')
            ->get();

        // Format data for chart
        $chartData = [
            'labels' => [],
            'datasets' => [],
        ];

        // Create a map of discipline IDs to colors
        $colorMap = [];
        $colors = [
            'rgba(79, 70, 229, 0.8)', // indigo-600
            'rgba(16, 185, 129, 0.8)', // emerald-500
            'rgba(245, 158, 11, 0.8)', // amber-500
            'rgba(239, 68, 68, 0.8)',  // red-500
            'rgba(59, 130, 246, 0.8)', // blue-500
            'rgba(236, 72, 153, 0.8)', // pink-500
            'rgba(139, 92, 246, 0.8)', // purple-500
        ];

        foreach ($disciplines as $index => $discipline) {
            $colorMap[$discipline->id] = $colors[$index % count($colors)];
        }

        // Group by date
        $dateData = [];
        foreach ($studySessionTotals as $total) {
            $date = $total->study_date->format('Y-m-d');
            if (! isset($dateData[$date])) {
                $dateData[$date] = [];
            }
            $dateData[$date][$total->discipline_id] = $total->total_duration_seconds / 60; // Convert to minutes
        }

        // Fill in missing dates
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateString = $currentDate->format('Y-m-d');
            $formattedDate = $currentDate->format('d/m');
            $chartData['labels'][] = $formattedDate;

            if (! isset($dateData[$dateString])) {
                $dateData[$dateString] = [];
            }

            $currentDate->addDay();
        }

        // Create datasets for each discipline
        foreach ($disciplines as $discipline) {
            $dataset = [
                'label' => $discipline->name,
                'backgroundColor' => $colorMap[$discipline->id],
                'data' => [],
            ];

            foreach ($chartData['labels'] as $index => $label) {
                $dateString = Carbon::now()->subDays(30 - $index)->format('Y-m-d');
                $dataset['data'][] = $dateData[$dateString][$discipline->id] ?? 0;
            }

            $chartData['datasets'][] = $dataset;
        }

        return $chartData;
    }
}
