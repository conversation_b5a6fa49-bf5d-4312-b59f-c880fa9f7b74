<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\Note;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NoteController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'content' => 'required|string',
            'lesson_id' => 'required|exists:lessons,id',
        ]);

        // Check if the lesson belongs to the authenticated user
        $lesson = Lesson::findOrFail($validated['lesson_id']);
        $this->authorize('view', $lesson);

        $note = Auth::user()->notes()->create([
            'content' => $validated['content'],
            'lesson_id' => $validated['lesson_id'],
        ]);

        return redirect()->back()
            ->with('success', 'Anotação adicionada com sucesso!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Note $note)
    {
        // Check if the note belongs to the authenticated user
        if ($note->user_id !== Auth::id()) {
            return redirect()->back()
                ->with('error', 'Você não tem permissão para editar esta anotação.');
        }

        $validated = $request->validate([
            'content' => 'required|string',
        ]);

        $note->update([
            'content' => $validated['content'],
        ]);

        return redirect()->back()
            ->with('success', 'Anotação atualizada com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Note $note)
    {
        // Check if the note belongs to the authenticated user
        if ($note->user_id !== Auth::id()) {
            return redirect()->back()
                ->with('error', 'Você não tem permissão para excluir esta anotação.');
        }

        $note->delete();

        return redirect()->back()
            ->with('success', 'Anotação excluída com sucesso!');
    }
}
