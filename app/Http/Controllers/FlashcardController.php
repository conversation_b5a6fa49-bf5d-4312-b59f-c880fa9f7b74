<?php

namespace App\Http\Controllers;

use App\Models\Flashcard;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;

class FlashcardController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Lesson $lesson)
    {
        $validated = $request->validate([
            'front' => 'required|string|max:255',
            'back' => 'required|string',
        ]);

        $flashcard = $lesson->flashcards()->create($validated);

        // Verificar se a requisição espera JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Flashcard adicionado com sucesso!',
                'flashcard' => $flashcard,
            ]);
        }

        return Redirect::back()->with('success', 'Flashcard adicionado com sucesso!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Flashcard $flashcard)
    {
        $validated = $request->validate([
            'front' => 'required|string|max:255',
            'back' => 'required|string',
        ]);

        $flashcard->update($validated);

        // Verificar se a requisição espera JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Flashcard atualizado com sucesso!',
                'flashcard' => $flashcard,
            ]);
        }

        return Redirect::back()->with('success', 'Flashcard atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Flashcard $flashcard)
    {
        // Armazenar o ID da lição antes de excluir o flashcard
        $lessonId = $flashcard->lesson_id;

        // Excluir o flashcard
        $flashcard->delete();

        return response()->json([
            'success' => true,
            'message' => 'Flashcard excluído com sucesso!',
            'lesson_id' => $lessonId,
        ]);
    }
}
