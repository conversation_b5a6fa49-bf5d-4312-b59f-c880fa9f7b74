<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\QuestionLink;
use Illuminate\Http\Request;
use Inertia\Inertia;

class QuestionLinkController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'url' => 'required|url|max:255',
            'lesson_id' => 'required|exists:lessons,id',
        ]);

        $lesson = Lesson::findOrFail($validated['lesson_id']);
        $this->authorize('view', $lesson);

        $questionLink = QuestionLink::create($validated);

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Link de questões adicionado com sucesso!');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(QuestionLink $questionLink)
    {
        $this->authorize('view', $questionLink->lesson);

        return Inertia::render('QuestionLinks/Edit', [
            'questionLink' => $questionLink,
            'lesson' => $questionLink->lesson,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, QuestionLink $questionLink)
    {
        $this->authorize('view', $questionLink->lesson);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'url' => 'required|url|max:255',
        ]);

        $questionLink->update($validated);

        return redirect()->route('lessons.show', $questionLink->lesson)
            ->with('success', 'Link de questões atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(QuestionLink $questionLink)
    {
        $this->authorize('view', $questionLink->lesson);

        $lesson = $questionLink->lesson;
        $questionLink->delete();

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Link de questões excluído com sucesso!');
    }
}
