<?php

namespace App\Http\Controllers;

use App\Imports\QuestionsImport;
use App\Models\Lesson;
use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Maatwebsite\Excel\Facades\Excel;

class QuestionController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Lesson $lesson)
    {
        $validated = $request->validate([
            'text' => 'required|string|max:1000',
            'explanation' => 'nullable|string|max:1000',
            'alternatives' => 'required|array|min:2|max:6',
            'alternatives.*.text' => 'required|string|max:500',
            'correct_alternative' => 'required|integer|min:0|max:5',
        ]);

        $question = $lesson->questions()->create([
            'text' => $validated['text'],
            'explanation' => $validated['explanation'],
        ]);

        foreach ($validated['alternatives'] as $index => $alternative) {
            $question->alternatives()->create([
                'text' => $alternative['text'],
                'is_correct' => $index === $validated['correct_alternative'],
            ]);
        }

        // Recarregar a questão com suas alternativas
        $question->load('alternatives');

        // Verificar se a requisição espera JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Questão adicionada com sucesso!',
                'question' => $question,
            ]);
        }

        return Redirect::back()->with('success', 'Questão adicionada com sucesso!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Question $question)
    {
        $validated = $request->validate([
            'text' => 'required|string|max:1000',
            'explanation' => 'nullable|string|max:1000',
            'alternatives' => 'required|array|min:2|max:6',
            'alternatives.*.text' => 'required|string|max:500',
            'alternatives.*.id' => 'nullable|integer|exists:alternatives,id',
            'correct_alternative' => 'required|integer|min:0|max:5',
        ]);

        $question->update([
            'text' => $validated['text'],
            'explanation' => $validated['explanation'],
        ]);

        // Delete existing alternatives
        $question->alternatives()->delete();

        // Create new alternatives
        foreach ($validated['alternatives'] as $index => $alternative) {
            $question->alternatives()->create([
                'text' => $alternative['text'],
                'is_correct' => $index === $validated['correct_alternative'],
            ]);
        }

        // Recarregar a questão com suas alternativas
        $question->load('alternatives');

        // Verificar se a requisição espera JSON
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Questão atualizada com sucesso!',
                'question' => $question,
            ]);
        }

        return Redirect::back()->with('success', 'Questão atualizada com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Question $question)
    {
        // Armazenar o ID da lição antes de excluir a questão
        $lessonId = $question->lesson_id;

        // Excluir apenas as alternativas relacionadas
        $question->alternatives()->delete();

        // Excluir a questão
        $question->delete();

        return response()->json([
            'success' => true,
            'message' => 'Questão excluída com sucesso!',
            'lesson_id' => $lessonId,
        ]);
    }

    /**
     * Check the answers for a question.
     */
    public function checkAnswer(Request $request, Question $question)
    {
        $validated = $request->validate([
            'selected_alternative_id' => 'required|integer|exists:alternatives,id',
        ]);

        $selectedAlternative = $question->alternatives()->findOrFail($validated['selected_alternative_id']);
        $correctAlternative = $question->correctAlternative();

        return response()->json([
            'is_correct' => $selectedAlternative->is_correct,
            'correct_alternative_id' => $correctAlternative->id,
            'explanation' => $question->explanation,
        ]);
    }

    /**
     * Import questions from Excel file.
     */
    public function import(Request $request, Lesson $lesson)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv',
        ]);

        $import = new QuestionsImport($lesson->id);

        Excel::import($import, $request->file('file'));

        $importedCount = $import->getImportedCount();
        $errors = $import->getErrors();

        if (count($errors) > 0) {
            return Redirect::back()->with([
                'success' => "Importação concluída. $importedCount questões importadas com sucesso.",
                'warning' => 'Ocorreram alguns problemas durante a importação:',
                'import_errors' => $errors,
            ]);
        }

        return Redirect::back()->with('success', "Importação concluída. $importedCount questões importadas com sucesso.");
    }
}
