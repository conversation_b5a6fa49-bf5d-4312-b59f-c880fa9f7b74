<?php

namespace App\Http\Controllers;

use App\Models\Discipline;
use App\Models\Lesson;
use App\Services\RevisionScheduler;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class LessonController extends Controller
{
    protected $revisionScheduler;

    public function __construct(RevisionScheduler $revisionScheduler)
    {
        $this->revisionScheduler = $revisionScheduler;
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $lessons = Auth::user()->lessons()->with('discipline')->latest()->get();
        $disciplines = Auth::user()->disciplines()->get();

        return Inertia::render('Lessons/Index', [
            'lessons' => $lessons,
            'disciplines' => $disciplines,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $disciplines = Auth::user()->disciplines()->get();
        $disciplineId = $request->query('discipline_id');

        return Inertia::render('Lessons/Create', [
            'disciplines' => $disciplines,
            'disciplineId' => $disciplineId,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'study_date' => 'required|date',
            'discipline_id' => 'required|exists:disciplines,id',
        ]);

        // Check if the discipline belongs to the authenticated user
        $discipline = Discipline::findOrFail($validated['discipline_id']);
        $this->authorize('view', $discipline);

        $lesson = Auth::user()->lessons()->create($validated);

        // Schedule revisions for this lesson
        $this->revisionScheduler->scheduleRevisionsForLesson($lesson);

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Aula cadastrada com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Lesson $lesson)
    {
        $this->authorize('view', $lesson);

        $lesson->load([
            'discipline',
            'mindMap',
            'questionLinks',
            'audioRecordings',
            'revisions' => function ($query) {
                $query->orderBy('scheduled_date');
            },
            'questions.alternatives',
            'flashcards',
            'notes',
        ]);

        // Adicionar URLs dos áudios
        $lesson->audioRecordings->each(function ($audio) {
            $audio->audio_url = Storage::url($audio->file_path);
        });

        return Inertia::render('Lessons/Show', [
            'lesson' => $lesson,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Lesson $lesson)
    {
        $this->authorize('update', $lesson);

        $disciplines = Auth::user()->disciplines()->get();

        return Inertia::render('Lessons/Edit', [
            'lesson' => $lesson,
            'disciplines' => $disciplines,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Lesson $lesson)
    {
        $this->authorize('update', $lesson);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'study_date' => 'required|date',
            'discipline_id' => 'required|exists:disciplines,id',
        ]);

        // Check if the discipline belongs to the authenticated user
        $discipline = Discipline::findOrFail($validated['discipline_id']);
        $this->authorize('view', $discipline);

        $lesson->update($validated);

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Aula atualizada com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Lesson $lesson)
    {
        $this->authorize('delete', $lesson);

        $lesson->delete();

        return redirect()->route('lessons.index')
            ->with('success', 'Aula excluída com sucesso!');
    }
}
