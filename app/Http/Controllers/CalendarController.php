<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\Revision;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CalendarController extends Controller
{
    /**
     * Display the calendar view.
     */
    public function index(Request $request)
    {
        $month = $request->query('month', Carbon::now()->month);
        $year = $request->query('year', Carbon::now()->year);

        $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth();
        $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth();

        // Get all revisions for the selected month
        $revisions = Revision::with('lesson.discipline')
            ->whereHas('lesson', function ($query) {
                $query->where('user_id', auth()->id());
            })
            ->whereBetween('scheduled_date', [$startDate, $endDate])
            ->get();

        // Get all lessons for the selected month
        $lessons = Lesson::with('discipline')
            ->where('user_id', auth()->id())
            ->whereBetween('study_date', [$startDate, $endDate])
            ->get();

        // Format the data for the calendar
        $calendarData = [];

        // Add lessons to calendar data
        foreach ($lessons as $lesson) {
            $date = Carbon::parse($lesson->study_date)->format('Y-m-d');

            if (! isset($calendarData[$date])) {
                $calendarData[$date] = [
                    'lessons' => [],
                    'revisions' => [],
                ];
            }

            $calendarData[$date]['lessons'][] = [
                'id' => $lesson->id,
                'title' => $lesson->title,
                'discipline' => $lesson->discipline->name,
                'type' => 'lesson',
            ];
        }

        // Add revisions to calendar data
        foreach ($revisions as $revision) {
            $date = Carbon::parse($revision->scheduled_date)->format('Y-m-d');

            if (! isset($calendarData[$date])) {
                $calendarData[$date] = [
                    'lessons' => [],
                    'revisions' => [],
                ];
            }

            $calendarData[$date]['revisions'][] = [
                'id' => $revision->id,
                'lesson_title' => $revision->lesson->title,
                'discipline' => $revision->lesson->discipline->name,
                'type' => $revision->revision_type,
                'is_completed' => $revision->is_completed,
                'scheduled_date' => $revision->scheduled_date,
            ];
        }

        return Inertia::render('Calendar/Index', [
            'calendarData' => $calendarData,
            'month' => (int) $month,
            'year' => (int) $year,
        ]);
    }
}
