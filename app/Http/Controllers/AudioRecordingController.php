<?php

namespace App\Http\Controllers;

use App\Models\AudioRecording;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class AudioRecordingController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // dd($request->file('audio_file'));

        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'duration_seconds' => 'nullable|integer',
                'lesson_id' => 'required|exists:lessons,id',
                'audio_file' => 'required|file|mimes:mp3,wav,ogg,webm,m4a|max:20480', // 20MB max, mais formatos
            ]);

            $lesson = Lesson::findOrFail($validated['lesson_id']);
            $this->authorize('view', $lesson);

            // Verificar se o arquivo foi enviado corretamente
            if (! $request->hasFile('audio_file') || ! $request->file('audio_file')->isValid()) {
                if ($request->expectsJson()) {
                    return response()->json(['error' => 'Arquivo de áudio inválido'], 422);
                }

                return redirect()->back()->with('error', 'Arquivo de áudio inválido');
            }

            // Obter informações do arquivo
            $file = $request->file('audio_file');
            $extension = $file->getClientOriginalExtension() ?: 'mp3';
            $fileName = 'audio_'.time().'.'.$extension;

            // Armazenar o arquivo
            $path = $file->storeAs('audio_recordings', $fileName, 'public');

            // Criar o registro no banco de dados
            $audioRecording = AudioRecording::create([
                'title' => $validated['title'],
                'file_path' => $path,
                'duration_seconds' => $validated['duration_seconds'] ?? 0, // Valor padrão para evitar nulos
                'lesson_id' => $validated['lesson_id'],
            ]);

            // Responder de acordo com o tipo de requisição
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Áudio de revisão gravado com sucesso!',
                    'audio' => $audioRecording,
                    'audioUrl' => Storage::url($path),
                ]);
            }

            return redirect()->route('lessons.show', $lesson)
                ->with('success', 'Áudio de revisão gravado com sucesso!');
        } catch (\Exception $e) {

            // Responder de acordo com o tipo de requisição
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Erro ao salvar o áudio: '.$e->getMessage()], 500);
            }

            return redirect()->back()->with('error', 'Erro ao salvar o áudio: '.$e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AudioRecording $audioRecording)
    {
        $this->authorize('view', $audioRecording->lesson);

        // Carregar o relacionamento lesson e discipline
        $audioRecording->load(['lesson.discipline']);

        return Inertia::render('AudioRecordings/Show', [
            'audioRecording' => $audioRecording,
            'audioUrl' => Storage::url($audioRecording->file_path),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AudioRecording $audioRecording)
    {
        $this->authorize('view', $audioRecording->lesson);

        return Inertia::render('AudioRecordings/Edit', [
            'audioRecording' => $audioRecording,
            'lesson' => $audioRecording->lesson,
            'audioUrl' => Storage::url($audioRecording->file_path),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AudioRecording $audioRecording)
    {
        $this->authorize('view', $audioRecording->lesson);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'audio_file' => 'nullable|file|mimes:mp3,wav,ogg|max:20480', // 20MB max
            'duration_seconds' => 'nullable|integer',
        ]);

        $data = [
            'title' => $validated['title'],
            'duration_seconds' => $validated['duration_seconds'] ?? $audioRecording->duration_seconds,
        ];

        // If a new audio file is uploaded, store it and update the path
        if ($request->hasFile('audio_file')) {
            // Delete the old file
            Storage::disk('public')->delete($audioRecording->file_path);

            // Store the new file
            $path = $request->file('audio_file')->store('audio_recordings', 'public');
            $data['file_path'] = $path;
        }

        $audioRecording->update($data);

        return redirect()->route('lessons.show', $audioRecording->lesson)
            ->with('success', 'Áudio de revisão atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AudioRecording $audioRecording)
    {
        $this->authorize('view', $audioRecording->lesson);

        $lesson = $audioRecording->lesson;

        // Delete the file from storage
        Storage::disk('public')->delete($audioRecording->file_path);

        $audioRecording->delete();

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Áudio de revisão excluído com sucesso!');
    }
}
