<?php

namespace App\Http\Controllers;

use App\Models\DisciplineGroup;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class GeneralReviewController extends Controller
{
    /**
     * Display the general review page.
     */
    public function index(Request $request)
    {
        // Get all discipline groups for the authenticated user
        $disciplineGroups = DisciplineGroup::where('user_id', Auth::id())
            ->with(['disciplines' => function ($query) {
                $query->orderBy('name');
            }])
            ->orderBy('name')
            ->get();

        // Initialize variables
        $selectedGroup = null;
        $selectedDiscipline = null;
        $lessons = collect();

        // If group and discipline are selected, get the lessons
        if ($request->has('group_id') && $request->has('discipline_id')) {
            $groupId = $request->get('group_id');
            $disciplineId = $request->get('discipline_id');

            // Verify that the group belongs to the user
            $selectedGroup = $disciplineGroups->firstWhere('id', $groupId);
            
            if ($selectedGroup) {
                // Verify that the discipline belongs to the selected group
                $selectedDiscipline = $selectedGroup->disciplines->firstWhere('id', $disciplineId);
                
                if ($selectedDiscipline) {
                    // Get all lessons for the selected discipline
                    $lessons = Lesson::where('discipline_id', $disciplineId)
                        ->orderBy('created_at', 'desc')
                        ->get()
                        ->map(function ($lesson) {
                            return [
                                'id' => $lesson->id,
                                'title' => $lesson->title,
                                'description' => $lesson->description,
                                'created_at' => $lesson->created_at,
                                'formatted_date' => $lesson->created_at->format('d/m/Y'),
                                'has_mind_map' => !empty($lesson->mind_map_content),
                                'has_audio' => $lesson->audioRecordings()->exists(),
                                'has_notes' => $lesson->notes()->exists(),
                                'has_flashcards' => $lesson->flashcards()->exists(),
                                'has_questions' => $lesson->questions()->exists(),
                                'has_question_links' => $lesson->questionLinks()->exists(),
                            ];
                        });
                }
            }
        }

        return Inertia::render('GeneralReview/Index', [
            'disciplineGroups' => $disciplineGroups,
            'selectedGroup' => $selectedGroup,
            'selectedDiscipline' => $selectedDiscipline,
            'lessons' => $lessons,
            'filters' => [
                'group_id' => $request->get('group_id'),
                'discipline_id' => $request->get('discipline_id'),
            ],
        ]);
    }
}
