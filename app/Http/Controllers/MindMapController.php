<?php

namespace App\Http\Controllers;

use App\Models\Lesson;
use App\Models\MindMap;
use Illuminate\Http\Request;
use Inertia\Inertia;

class MindMapController extends Controller
{
    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $lessonId = $request->query('lesson_id');
        $lesson = Lesson::findOrFail($lessonId);

        $this->authorize('view', $lesson);

        return Inertia::render('MindMaps/Create', [
            'lesson' => $lesson,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'content' => 'required|string',
            'lesson_id' => 'required|exists:lessons,id',
        ]);

        $lesson = Lesson::findOrFail($validated['lesson_id']);
        $this->authorize('view', $lesson);

        // Check if a mind map already exists for this lesson
        $existingMindMap = $lesson->mindMap;

        if ($existingMindMap) {
            $existingMindMap->update([
                'content' => $validated['content'],
            ]);
        } else {
            MindMap::create($validated);
        }

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Mapa mental salvo com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(MindMap $mindMap)
    {
        $this->authorize('view', $mindMap->lesson);

        return Inertia::render('MindMaps/Show', [
            'mindMap' => $mindMap,
            'lesson' => $mindMap->lesson,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(MindMap $mindMap)
    {
        $this->authorize('view', $mindMap->lesson);

        // Carregar explicitamente o relacionamento lesson
        $mindMap->load('lesson');

        return Inertia::render('MindMaps/Edit', [
            'mindMap' => $mindMap,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MindMap $mindMap)
    {
        $this->authorize('view', $mindMap->lesson);

        $validated = $request->validate([
            'content' => 'required|string',
        ]);

        $mindMap->update($validated);

        return redirect()->route('lessons.show', $mindMap->lesson)
            ->with('success', 'Mapa mental atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MindMap $mindMap)
    {
        $this->authorize('view', $mindMap->lesson);

        $lesson = $mindMap->lesson;
        $mindMap->delete();

        return redirect()->route('lessons.show', $lesson)
            ->with('success', 'Mapa mental excluído com sucesso!');
    }
}
