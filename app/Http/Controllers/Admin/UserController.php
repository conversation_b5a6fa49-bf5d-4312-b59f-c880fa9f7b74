<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index(Request $request)
    {
        $search = $request->get('search');
        $status = $request->get('status');

        $query = User::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($status) {
            $query->where('status', $status);
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15);

        // Estatísticas
        $stats = [
            'total' => User::count(),
            'active' => User::where('status', 'active')->count(),
            'pending' => User::where('status', 'pending')->count(),
            'blocked' => User::where('status', 'blocked')->count(),
        ];

        // Últimos usuários cadastrados
        $recentUsers = User::orderBy('created_at', 'desc')->take(5)->get();

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'filters' => [
                'search' => $search,
                'status' => $status,
            ],
        ]);
    }

    /**
     * Approve a pending user
     */
    public function approve(User $user)
    {
        if ($user->isPending()) {
            $user->activate();

            return back()->with('success', "Usuário {$user->name} foi aprovado com sucesso.");
        }

        return back()->with('error', 'Este usuário não está pendente de aprovação.');
    }

    /**
     * Block a user
     */
    public function block(User $user)
    {
        if ($user->isAdmin()) {
            return back()->with('error', 'Não é possível bloquear um administrador.');
        }

        if (! $user->isBlocked()) {
            $user->block();

            return back()->with('success', "Usuário {$user->name} foi bloqueado com sucesso.");
        }

        return back()->with('error', 'Este usuário já está bloqueado.');
    }

    /**
     * Unblock a user
     */
    public function unblock(User $user)
    {
        if ($user->isBlocked()) {
            $user->unblock();

            return back()->with('success', "Usuário {$user->name} foi desbloqueado com sucesso.");
        }

        return back()->with('error', 'Este usuário não está bloqueado.');
    }

    /**
     * Delete a user
     */
    public function destroy(User $user)
    {
        if ($user->isAdmin()) {
            return back()->with('error', 'Não é possível excluir um administrador.');
        }

        if ($user->id === auth()->id()) {
            return back()->with('error', 'Você não pode excluir sua própria conta.');
        }

        $userName = $user->name;
        $user->delete();

        return back()->with('success', "Usuário {$userName} foi excluído com sucesso.");
    }

    /**
     * Show pending users for approval
     */
    public function pending()
    {
        $pendingUsers = User::where('status', 'pending')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $pendingUsers,
        ]);
    }
}
