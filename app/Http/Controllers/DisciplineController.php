<?php

namespace App\Http\Controllers;

use App\Models\Discipline;
use App\Models\DisciplineGroup;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DisciplineController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Get all discipline groups for the current user with their disciplines
        $disciplineGroups = auth()->user()->disciplineGroups()
            ->with(['disciplines' => function ($query) {
                $query->with('lessons')->latest();
            }])
            ->latest()
            ->get();

        // Get all disciplines that don't belong to any group
        $ungroupedDisciplines = auth()->user()->disciplines()
            ->whereNull('discipline_group_id')
            ->with('lessons')
            ->latest()
            ->get();

        return Inertia::render('Disciplines/Index', [
            'disciplineGroups' => $disciplineGroups,
            'ungroupedDisciplines' => $ungroupedDisciplines,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get all discipline groups for the current user
        $disciplineGroups = auth()->user()->disciplineGroups()->latest()->get();

        return Inertia::render('Disciplines/Create', [
            'disciplineGroups' => $disciplineGroups,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'discipline_group_id' => 'nullable|exists:discipline_groups,id',
        ]);

        // Check if the discipline group belongs to the current user
        if (! empty($validated['discipline_group_id'])) {
            $disciplineGroup = DisciplineGroup::find($validated['discipline_group_id']);
            if (! $disciplineGroup || $disciplineGroup->user_id !== auth()->id()) {
                unset($validated['discipline_group_id']);
            }
        }

        $discipline = auth()->user()->disciplines()->create($validated);

        return redirect()->route('disciplines.show', $discipline)
            ->with('success', 'Disciplina criada com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Discipline $discipline)
    {
        $this->authorize('view', $discipline);

        $discipline->load('lessons');

        return Inertia::render('Disciplines/Show', [
            'discipline' => $discipline,
            'lessons' => $discipline->lessons()->latest()->get(),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Discipline $discipline)
    {
        $this->authorize('update', $discipline);

        // Get all discipline groups for the current user
        $disciplineGroups = auth()->user()->disciplineGroups()->latest()->get();

        return Inertia::render('Disciplines/Edit', [
            'discipline' => $discipline,
            'disciplineGroups' => $disciplineGroups,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Discipline $discipline)
    {
        $this->authorize('update', $discipline);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'discipline_group_id' => 'nullable|exists:discipline_groups,id',
        ]);

        // Check if the discipline group belongs to the current user
        if (! empty($validated['discipline_group_id'])) {
            $disciplineGroup = DisciplineGroup::find($validated['discipline_group_id']);
            if (! $disciplineGroup || $disciplineGroup->user_id !== auth()->id()) {
                unset($validated['discipline_group_id']);
            }
        }

        $discipline->update($validated);

        return redirect()->route('disciplines.show', $discipline)
            ->with('success', 'Disciplina atualizada com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Discipline $discipline)
    {
        $this->authorize('delete', $discipline);

        // Contar recursos relacionados para informar ao usuário
        $lessonsCount = $discipline->lessons()->count();
        $audioCount = 0;

        // Contar áudios se houver aulas
        if ($lessonsCount > 0) {
            $audioCount = \App\Models\AudioRecording::whereIn(
                'lesson_id',
                $discipline->lessons()->pluck('id')
            )->count();
        }

        // Excluir a disciplina (isso acionará o evento deleting)
        $discipline->delete();

        // Mensagem mais informativa
        $message = 'Disciplina excluída com sucesso!';
        if ($lessonsCount > 0) {
            $message .= " Também foram excluídas {$lessonsCount} aulas";
            if ($audioCount > 0) {
                $message .= " e {$audioCount} gravações de áudio";
            }
            $message .= ' relacionadas.';
        }

        return redirect()->route('disciplines.index')
            ->with('success', $message);
    }
}
